package top.continew.katai;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.atlassian.guava.common.util.concurrent.RateLimiter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import top.continew.katai.exception.ThirdException;
import top.continew.katai.itl.ItlConfig;
import top.continew.katai.itl.model.req.*;
import top.continew.katai.itl.model.resp.*;
import top.continew.katai.itl.model.resp.ItlAccountBalanceResp;
import top.continew.katai.okhttp.ClientHelper;
import top.continew.katai.utils.Common;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @version: 1.00.00
 * @description: Interlace卡台API客户端
 * @date: 2025/3/11 14:13
 */
@Slf4j
public class ItlClient extends Client {
    private final String clientId;
    private final String clientSecret;
    @Setter
    private String accessToken;
    @Setter
    private String refreshToken;
    
    // 接口限流控制
    private final RateLimiter rateLimiter = RateLimiter.create(5);

    public ItlClient(ItlConfig config) {
        super(config);
        this.clientId = config.getClientId();
        this.clientSecret = config.getClientSecret();
    }

    /**
     * 获取授权码
     * @return 授权码响应
     */
    public ItlAuthCodeResp getAuthCode() {
        Params params = new Params().setProtocol("HTTPS").setMethod("GET").setPathname("/open-api/oauth/authorize");
        OpenApiRequest req = new OpenApiRequest();
        req.setQuery(Map.of("clientId", this.clientId));
        return Common.toModel(callApi(params, req), "body", ItlAuthCodeResp.class);
    }

    /**
     * 获取访问令牌
     * @param code 授权码
     * @return 令牌响应
     */
    public ItlTokenResp getToken(String code) {
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/open-api/oauth/access-token");
        OpenApiRequest req = new OpenApiRequest();
        req.addHeader("Content-Type", "application/json");
        
        Map<String, String> body = new HashMap<>();
        body.put("clientId", this.clientId);
        body.put("clientSecret", this.clientSecret);
        body.put("code", code);
        req.setBody(body);
        
        return Common.toModel(callApi(params, req), "body", ItlTokenResp.class);
    }

    /**
     * 刷新令牌
     * @param refreshToken 刷新令牌
     * @return 新的令牌响应
     */
    public ItlTokenResp refreshToken(String refreshToken) {
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/open-api/oauth/refresh-token");
        OpenApiRequest req = new OpenApiRequest();
        req.addHeader("Content-Type", "application/json");
        
        Map<String, String> body = new HashMap<>();
        body.put("clientId", this.clientId);
        body.put("refreshToken", refreshToken);
        req.setBody(body);
        
        return Common.toModel(callApi(params, req), "body", ItlTokenResp.class);
    }

    /**
     * 获取账户余额
     * @param limit 限制数量
     * @param page 页码
     * @return 账户余额响应
     */
    public ItlAccountBalanceResp getAccountBalances(Integer limit, Integer page) {
        Params params = new Params().setProtocol("HTTPS").setMethod("GET").setPathname("/open-api/v1/balances");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);
        
        Map<String, String> query = new HashMap<>();
        if (limit != null) {
            query.put("limit", String.valueOf(limit));
        }
        if (page != null) {
            query.put("page", String.valueOf(page));
        }
        query.put("walletType", "Budget");
        req.setQuery(query);
        
        return Common.toModel(callApi(params, req), "body", ItlAccountBalanceResp.class);
    }

    /**
     * 获取所有账户
     * @param limit 限制数量
     * @param page 页码
     * @return 账户列表响应
     */
    public ItlAccountListResp getAccounts(Integer limit, Integer page) {
        Params params = new Params().setProtocol("HTTPS").setMethod("GET").setPathname("/open-api/v1/accounts");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);
        
        Map<String, String> query = new HashMap<>();
        if (limit != null) {
            query.put("limit", String.valueOf(limit));
        }
        if (page != null) {
            query.put("page", String.valueOf(page));
        }
        req.setQuery(query);
        
        return Common.toModel(callApi(params, req), "body", ItlAccountListResp.class);
    }

    /**
     * 创建持卡人
     * @param accountId 账户ID
     * @param createCardholderReq 创建持卡人请求
     * @return 创建持卡人响应
     */
    public ItlCreateCardholderResp createCardholder(String accountId, ItlCreateCardholderReq createCardholderReq) {
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/open-api/v2/cards/" + accountId + "/cardholder");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);
        req.addHeader("Content-Type", "application/json");
        req.setBody(createCardholderReq);
        
        return Common.toModel(callApi(params, req), "body", ItlCreateCardholderResp.class);
    }

    /**
     * 获取持卡人列表
     * @param accountId 账户ID
     * @return 持卡人列表响应
     */
    public ItlCardholderListResp getCardholders(String accountId) {
        Params params = new Params().setProtocol("HTTPS").setMethod("GET").setPathname("/open-api/v2/cards/" + accountId + "/cardholders");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);
        
        return Common.toModel(callApi(params, req), "body", ItlCardholderListResp.class);
    }

    /**
     * 获取所有可用的卡BIN
     * @return 卡BIN列表响应
     */
    public ItlCardBinResp getCardBins() {
        Params params = new Params().setProtocol("HTTPS").setMethod("GET").setPathname("/open-api/v1/cards/bins");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);
        
        return Common.toModel(callApi(params, req), "body", ItlCardBinResp.class);
    }

    /**
     * 开卡
     * @param cardOpenReq 开卡请求
     * @return 开卡响应
     */
    public Boolean openCard(ItlCardOpenReq cardOpenReq) {
        Params params = new Params().setProtocol("HTTPS").setMethod("POST").setPathname("/open-api/v2/cards");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);
        req.addHeader("Content-Type", "application/json");
        req.setBody(cardOpenReq);
        
        Boolean result = Common.toModel(callApi(params, req), "body", Boolean.class);

        return result;
    }

    /**
     * 获取所有卡片
     * @param limit 限制数量
     * @param page 页码
     * @return 卡片列表响应
     */
    public ItlCardListResp getCards(Integer limit, Integer page) {
        Params params = new Params().setProtocol("HTTPS").setMethod("GET").setPathname("/open-api/v2/cards");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);
        
        Map<String, String> query = new HashMap<>();
        if (limit != null) {
            query.put("limit", String.valueOf(limit));
        }
        if (page != null) {
            query.put("page", String.valueOf(page));
        }
        req.setQuery(query);
        
        return Common.toModel(callApi(params, req), "body", ItlCardListResp.class);
    }

    /**
     * 冻结卡
     * @param freezeCardReq 冻结卡请求
     */
    public void freezeCard(ItlFreezeCardReq freezeCardReq) {
        Params params = new Params().setProtocol("HTTPS").setMethod("PUT").setPathname("/open-api/v1/cards/suspend");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);
        req.addHeader("Content-Type", "application/json");
        req.setBody(freezeCardReq);
        
        callApi(params, req);
    }

    /**
     * 解冻卡
     * @param enableCardReq 解冻卡请求
     */
    public void enableCard(ItlEnableCardReq enableCardReq) {
        Params params = new Params().setProtocol("HTTPS").setMethod("PUT").setPathname("/open-api/v1/cards/enable");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);
        req.addHeader("Content-Type", "application/json");
        req.setBody(enableCardReq);
        
        callApi(params, req);
    }

    /**
     * 修改卡限额
     * @param updateCardLimitReq 修改卡限额请求
     */
    public void updateCardLimit(ItlUpdateCardLimitReq updateCardLimitReq) {
        Params params = new Params().setProtocol("HTTPS").setMethod("PUT").setPathname("/open-api/v1/cards/velocity-control");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);
        req.addHeader("Content-Type", "application/json");
        req.setBody(updateCardLimitReq);
        
        callApi(params, req);
    }

    /**
     * 获取卡片余额流水
     * @param cardId 卡片ID
     * @return 交易记录响应
     */
    public ItlCardTransactionResp getCardTransactions(String cardId) {
        Params params = new Params().setProtocol("HTTPS").setMethod("GET").setPathname("/open-api/v1/cards/transactions/" + cardId);
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);
        
        return Common.toModel(callApi(params, req), "body", ItlCardTransactionResp.class);
    }

    /**
     * 分页按时间段获取卡片余额流水
     * @param cardId 卡片ID
     * @param type 交易类型
     * @param status 交易状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @param page 页码
     * @return 交易记录列表响应
     */
    public ItlCardTransactionListResp getCardTransactions(String cardId, Integer type, Integer status, String startTime, String endTime, Integer limit, Integer page) {
        Params params = new Params().setProtocol("HTTPS").setMethod("GET").setPathname("/open-api/v1/cards/transactions");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);
        
        Map<String, String> query = new HashMap<>();
        query.put("cardId", cardId);
        if (type != null) {
            query.put("type", String.valueOf(type));
        }
        if (status != null) {
            query.put("status", String.valueOf(status));
        }
        if (startTime != null) {
            query.put("startTime", startTime);
        }
        if (endTime != null) {
            query.put("endTime", endTime);
        }
        if (limit != null) {
            query.put("limit", String.valueOf(limit));
        }
        if (page != null) {
            query.put("page", String.valueOf(page));
        }
        req.setQuery(query);
        
        return Common.toModel(callApi(params, req), "body", ItlCardTransactionListResp.class);
    }

    /**
     * 获取卡片敏感信息
     * @param cardId 卡片ID
     * @return 卡片敏感信息响应
     */
    public ItlCardSensitiveInfoResp getCardSensitiveInfo(String cardId) {
        Params params = new Params().setProtocol("HTTPS").setMethod("GET").setPathname("/open-api/v1/cards/info");
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);
        req.setQuery(Map.of("cardId", cardId));
        
        return Common.toModel(callApi(params, req), "body", ItlCardSensitiveInfoResp.class);
    }


    /**
     * 获取卡片信息
     * @param cardId 卡片ID
     * @return 卡片信息响应
     */
    public ItlCardDetailResp getCardDetail(String cardId) {
        Params params = new Params().setProtocol("HTTPS").setMethod("GET").setPathname("/open-api/v2/cards/" + cardId);
        OpenApiRequest req = new OpenApiRequest();
        setHeaderAuth(req);

        return Common.toModel(callApi(params, req), "body", ItlCardDetailResp.class);
    }

    /**
     * 更新卡片标签
     * @param cardId 卡片ID
     * @param label 标签
     * @param accessToken 授权token
     */
    public void updateCardLabel(String cardId, String label, String accessToken) throws Exception {
        String host = "api-global.interlace.money";
        int port = 443;
        RuntimeOptions options = new RuntimeOptions();
        OkHttpClient client = ClientHelper.getOkHttpClient(host, port, options);
        
        String url = "https://api-global.interlace.money/graphql";
        String json = "{\"operationName\":\"updateQbitCard\",\"variables\":{\"data\":{\"label\":\"" + label + "\",\"qbitCardId\":\"" + cardId + "\"}},\"query\":\"mutation updateQbitCard($data: UpdateQbitCardInput\\u0021) {\\n  updateQbitCard(data: $data) {\\n    id\\n    __typename\\n  }\\n}\\n\"}";
        
        Request request = new Request.Builder()
            .url(url)
            .post(RequestBody.create(json, MediaType.get("application/json")))
            .addHeader("Origin", "https://www.interlace.money")
            .addHeader("Referer", "https://www.interlace.money/")
            .addHeader("accept", "*/*")
            .addHeader("accept-language", "zh")
            .addHeader("authorization", "Bearer " + accessToken)
            .addHeader("content-type", "application/json")
            .addHeader("systemtype", "QbitInternational")
            .addHeader("website-version", "business")
            .build();
            
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new ThirdException(Map.of("code", response.code(), "message", response.message()));
            }
        }
    }

    /**
     * 调用API
     * @param params 参数
     * @param apiRequest API请求
     * @return 响应结果
     */
    private Map<String, ?> callApi(Params params, OpenApiRequest apiRequest) {
        RuntimeOptions runtime = new RuntimeOptions();
        apiRequest.addHeader("Content-Type", "application/json");
        if (rateLimiter.tryAcquire(30, TimeUnit.SECONDS)) {
            return doCallApi(params, apiRequest, runtime);
        } else {
            throw new ThirdException(Map.of("code", "429", "message", "接口限流，请稍后再试"));
        }
    }

    /**
     * 执行API调用
     * @param params 参数
     * @param apiRequest API请求
     * @param runtime 运行时选项
     * @return 响应结果
     */
    private Map<String, ?> doCallApi(Params params, OpenApiRequest apiRequest, RuntimeOptions runtime) {
        log.info("Interlace-请求URL:{},{}", params.getMethod(), params.getPathname());
        log.info("Interlace-请求body:{}", JSON.toJSONString(apiRequest.getBody()));
        log.info("Interlace-请求query:{}", JSON.toJSONString(apiRequest.getQuery()));
        log.debug("Interlace-请求headers:{}", JSON.toJSONString(apiRequest.getHeaders()));
        long startTime = System.currentTimeMillis();
        Map<String, ?> response;

//        String curlCommand = String.format(
//                "curl -X " + params.getMethod()+ " 'https://%s%s' \\\n" +
//                        "  -H 'Content-Type: application/json' \\\n" +
//                        "  %s \\\n" +
//                        "  -d '%s'",
//                config.endpoint,
//                params.getPathname(),
//                null == apiRequest.getHeaders() ? "" : apiRequest.getHeaders().entrySet().stream()
//                        .map(e -> String.format("-H '%s: %s'", e.getKey(), e.getValue()))
//                        .collect(Collectors.joining(" \\\n  ")),
//                JSON.toJSONString(apiRequest.getBody()).replace("'", "'\\''")
//        );
//        log.info("Interlace-curl命令:\n{}", curlCommand);

        try {
            response = this.doRequest(params, apiRequest, runtime);
        } catch (ThirdException e) {
            log.error("Interlace-请求异常:{}, {}", e.getCode(), e.getMessage());
            throw e;
        }

        long costTime = System.currentTimeMillis() - startTime;

        if(params.getPathname().contains("oauth/authorize") || params.getPathname().contains("oauth/access-token")){
            Map<String, Object> result = new HashMap<>();
            result.put("header", response.get("header"));
            result.put("body", response.get("body"));
            return result;
        }

        JSONObject body = JSONObject.from(response.get("body"));
        String code = body.getString("code");
        log.debug("Interlace-响应耗时:{}ms，响应码:{}", costTime, code);

        if (StrUtil.isNotBlank(code) && !"0".equals(code)) {
            throw new ThirdException(Map.of("code", body.get("code"), "message", body.get("message")));
        }

        Map<String, Object> result = new HashMap<>();
        result.put("header", response.get("header"));

        if(body.containsKey("pageTotal") || body.containsKey("total")) {
            JSONObject pageObj = new JSONObject();
            pageObj.put("pageTotal", body.get("pageTotal"));
            pageObj.put("total", body.get("total"));
            pageObj.put("data", body.get("data"));
            result.put("body", pageObj);
        }else {
            result.put("body", body.get("data"));
        }

        return result;
    }

    /**
     * 设置授权请求头
     * @param req 请求对象
     */
    private void setHeaderAuth(OpenApiRequest req) {
        if (StrUtil.isNotBlank(this.accessToken)) {
            req.addHeader("x-access-token", this.accessToken);
        }
    }
}
