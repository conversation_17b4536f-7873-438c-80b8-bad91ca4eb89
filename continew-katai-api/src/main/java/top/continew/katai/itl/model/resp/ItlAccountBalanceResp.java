package top.continew.katai.itl.model.resp;

import lombok.Data;

import java.util.List;

/**
 * @version: 1.00.00
 * @description: 账户余额响应
 * @date: 2025/8/21 14:30
 */
@Data
public class ItlAccountBalanceResp {
    /**
     * 账户余额数据列表
     */
    private List<ItlAccountBalance> data;
    
    /**
     * 总页数
     */
    private Integer pageTotal;
    
    /**
     * 总记录数
     */
    private Integer total;
    
    /**
     * 账户余额信息
     */
    @Data
    public static class ItlAccountBalance {
        /**
         * 余额ID
         */
        private String id;
        
        /**
         * 账户ID
         */
        private String accountId;
        
        /**
         * 创建时间
         */
        private String createTime;
        
        /**
         * 可用余额
         */
        private String available;
        
        /**
         * 待处理金额
         */
        private String pending;
        
        /**
         * 冻结金额
         */
        private String frozen;
        
        /**
         * 货币代码
         */
        private String currency;
        
        /**
         * 钱包类型
         */
        private String walletType;
    }
}