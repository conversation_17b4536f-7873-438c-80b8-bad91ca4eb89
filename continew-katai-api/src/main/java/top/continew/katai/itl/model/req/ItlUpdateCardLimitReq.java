package top.continew.katai.itl.model.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @version: 1.00.00
 * @description: 修改卡限额
 * @date: 2025/8/14 17:10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ItlUpdateCardLimitReq {
    private String cardId;

    /**
     * DAY MONTH YEAR LIFETIME
     */
    private String type;

    private String limit;
}
