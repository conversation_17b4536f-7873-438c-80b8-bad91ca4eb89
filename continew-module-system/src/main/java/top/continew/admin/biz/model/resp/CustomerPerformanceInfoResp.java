package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.enums.CustomerStatusEnum;
import top.continew.admin.biz.enums.CustomerTypeEnum;

import java.time.LocalDateTime;

@Data
public class CustomerPerformanceInfoResp {

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "客户类型")
    private CustomerTypeEnum type;

    @Schema(description = "客户状态")
    private CustomerStatusEnum status;

    @Schema(description = "合作时间")
    private LocalDateTime cooperateTime;

}
