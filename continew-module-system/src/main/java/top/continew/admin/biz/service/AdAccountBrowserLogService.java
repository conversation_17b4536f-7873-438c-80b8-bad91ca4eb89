/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.AdAccountBrowserLogDO;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.AdAccountBrowserLogQuery;
import top.continew.admin.biz.model.req.AdAccountBrowserLogReq;
import top.continew.admin.biz.model.resp.AdAccountBrowserLogDetailResp;
import top.continew.admin.biz.model.resp.AdAccountBrowserLogResp;

/**
 * 账号浏览器操作记录业务接口
 *
 * <AUTHOR>
 * @since 2024/12/31 14:27
 */
public interface AdAccountBrowserLogService extends BaseService<AdAccountBrowserLogResp, AdAccountBrowserLogDetailResp, AdAccountBrowserLogQuery, AdAccountBrowserLogReq>, IService<AdAccountBrowserLogDO> {}