package top.continew.admin.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import top.continew.admin.biz.model.entity.CustomerBusinessUserDO;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.CustomerBusinessUserQuery;
import top.continew.admin.biz.model.req.CustomerBusinessUserReq;
import top.continew.admin.biz.model.resp.CustomerBusinessUserDetailResp;
import top.continew.admin.biz.model.resp.CustomerBusinessUserResp;

import java.util.List;

/**
 * 客户商务对接业务接口
 *
 * <AUTHOR>
 * @since 2025/08/05 15:35
 */
public interface CustomerBusinessUserService extends BaseService<CustomerBusinessUserResp, CustomerBusinessUserDetailResp, CustomerBusinessUserQuery, CustomerBusinessUserReq>, IService<CustomerBusinessUserDO> {

    List<CustomerBusinessUserResp> getByCustomerId(Long id);
}