/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service;

import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.model.entity.CardBalanceDO;
import top.continew.admin.biz.model.query.CardBalanceQuery;
import top.continew.admin.biz.model.req.CardBalanceReq;
import top.continew.admin.biz.model.resp.CardBalanceDetailResp;
import top.continew.admin.biz.model.resp.CardBalanceResp;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 卡片余额流水业务接口
 *
 * <AUTHOR>
 * @since 2024/12/29 13:45
 */
public interface CardBalanceService extends BaseService<CardBalanceResp, CardBalanceDetailResp, CardBalanceQuery, CardBalanceReq>, IService<CardBalanceDO> {

    List<CardBalanceDO> listByPlatform(Integer platform);

    /**
     * 同步流水数据
     *
     * @param start
     * @param end
     */
    void syncData(LocalDateTime start, LocalDateTime end);

    /**
     * 同步流水数据
     *
     * @param platform
     * @param start
     * @param end
     */
    void syncData(CardPlatformEnum platform, LocalDateTime start, LocalDateTime end);

    void updateCardNumber(CardPlatformEnum platform);
}