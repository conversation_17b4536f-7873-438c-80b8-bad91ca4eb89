package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.CustomerQuestionDO;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.CustomerQuestionQuery;
import top.continew.admin.biz.model.req.CustomerQuestionReq;
import top.continew.admin.biz.model.resp.CustomerQuestionDetailResp;
import top.continew.admin.biz.model.resp.CustomerQuestionResp;

/**
 * 客户问题业务接口
 *
 * <AUTHOR>
 * @since 2025/04/30 14:35
 */
public interface CustomerQuestionService extends BaseService<CustomerQuestionResp, CustomerQuestionDetailResp, CustomerQuestionQuery, CustomerQuestionReq>, IService<CustomerQuestionDO> {

    Integer getQuestionNum(CustomerQuestionQuery query);
}