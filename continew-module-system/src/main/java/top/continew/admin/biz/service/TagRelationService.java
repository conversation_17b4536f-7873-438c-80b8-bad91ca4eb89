package top.continew.admin.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import top.continew.admin.biz.model.entity.TagRelationDO;
import top.continew.admin.biz.model.query.TagRelationQuery;
import top.continew.admin.biz.model.req.TagRelationReq;
import top.continew.admin.biz.model.resp.TagRelationDetailResp;
import top.continew.admin.biz.model.resp.TagRelationResp;
import top.continew.starter.extension.crud.service.BaseService;

import java.util.List;

/**
 * 标签关联业务接口
 *
 * <AUTHOR>
 * @since 2025/05/08 16:37
 */
public interface TagRelationService extends BaseService<TagRelationResp, TagRelationDetailResp, TagRelationQuery, TagRelationReq>, IService<TagRelationDO> {

    /**
     * 查询出满足条件的订单ID
     *
     * @param tagIds
     * @return
     */
    List<Long> listOrderId(List<Long> tagIds);

    /**
     * 查询出满足条件的广告户ID
     *
     * @param tagIds
     * @return
     */
    List<Long> listAdAccountId(List<Long> tagIds);


    String tagNamesByOrderId(Long orderId);
}