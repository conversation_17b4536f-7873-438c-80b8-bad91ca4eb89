package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.SettleOrderItemDO;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.SettleOrderItemQuery;
import top.continew.admin.biz.model.req.SettleOrderItemReq;
import top.continew.admin.biz.model.resp.SettleOrderItemDetailResp;
import top.continew.admin.biz.model.resp.SettleOrderItemResp;

/**
 * 结算订单详情业务接口
 *
 * <AUTHOR>
 * @since 2025/03/20 14:56
 */
public interface SettleOrderItemService extends BaseService<SettleOrderItemResp, SettleOrderItemDetailResp, SettleOrderItemQuery, SettleOrderItemReq>, IService<SettleOrderItemDO> {}