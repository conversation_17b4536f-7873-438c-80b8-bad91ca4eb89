package top.continew.admin.biz.service;

import top.continew.admin.biz.model.query.BusinessStatisticsQuery;
import top.continew.admin.biz.model.query.CustomerSpentTrendQuery;
import top.continew.admin.biz.model.query.CustomerStatisticsQuery;
import top.continew.admin.biz.model.resp.BusinessStatisticsResp;
import top.continew.admin.biz.model.resp.CustomerSpentTrendResp;
import top.continew.admin.biz.model.resp.CustomerStatisticsOverviewResp;
import top.continew.admin.biz.model.resp.CustomerStatisticsResp;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;

import java.util.List;

/**
 * 客户统计服务
 */
public interface CustomerStatisticsService {
    /**
     * 分页查询客户统计数据
     *
     * @param query 查询参数
     * @return 分页数据
     */
    PageResp<CustomerStatisticsResp> page(CustomerStatisticsQuery query);

    /**
     * 数据汇总
     *
     * @param query
     * @return
     */
    CustomerStatisticsOverviewResp selectCustomerStatSummary(CustomerStatisticsQuery query);

    List<CustomerStatisticsResp> listCustomerStat(CustomerStatisticsQuery query);

    PageResp<BusinessStatisticsResp> businessPage(BusinessStatisticsQuery query);

    List<BusinessStatisticsResp> listBusinessData(BusinessStatisticsQuery query);

    /**
     * 查询客户对比趋势
     *
     * @param customerSpentTrendQuery
     * @param query
     * @return
     */
    PageResp<CustomerSpentTrendResp> selectCustomerSpentTrendList(CustomerSpentTrendQuery customerSpentTrendQuery,
                                                                  PageQuery query);
}
