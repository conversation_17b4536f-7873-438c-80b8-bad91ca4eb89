package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.AdProductDO;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.AdProductQuery;
import top.continew.admin.biz.model.req.AdProductReq;
import top.continew.admin.biz.model.resp.AdProductDetailResp;
import top.continew.admin.biz.model.resp.AdProductResp;

/**
 * 投放产品业务接口
 *
 * <AUTHOR>
 * @since 2025/08/12 15:07
 */
public interface AdProductService extends BaseService<AdProductResp, AdProductDetailResp, AdProductQuery, AdProductReq>, IService<AdProductDO> {}