package top.continew.admin.biz.service;


import top.continew.admin.biz.model.query.OperationsRecordStatQuery;
import top.continew.admin.biz.model.query.OperationsStatQuery;
import top.continew.admin.biz.model.resp.OperationsStatResp;
import top.continew.admin.biz.model.resp.operationTaskStatResp;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;

public interface OperationsStatService {

    PageResp<OperationsStatResp> page(OperationsStatQuery query, PageQuery pageQuery);

    PageResp<operationTaskStatResp> operationTaskStatPage(OperationsRecordStatQuery query, PageQuery pageQuery);
}
