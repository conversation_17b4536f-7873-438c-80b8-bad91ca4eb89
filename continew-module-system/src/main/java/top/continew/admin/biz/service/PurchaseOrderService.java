package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.PurchaseOrderDO;
import top.continew.admin.biz.model.query.PurchaseOrderQuery;
import top.continew.admin.biz.model.req.IdsReq;
import top.continew.admin.biz.model.req.PurchaseOrderPayReq;
import top.continew.admin.biz.model.req.PurchaseOrderReceiveReq;
import top.continew.admin.biz.model.req.PurchaseOrderReq;
import top.continew.admin.biz.model.resp.PurchaseOrderCheckResp;
import top.continew.admin.biz.model.resp.PurchaseOrderDetailResp;
import top.continew.admin.biz.model.resp.PurchaseOrderResp;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购订单业务接口
 *
 * <AUTHOR>
 * @since 2025/05/21 14:38
 */
public interface PurchaseOrderService extends BaseService<PurchaseOrderResp, PurchaseOrderDetailResp, PurchaseOrderQuery, PurchaseOrderReq>, IService<PurchaseOrderDO> {

    /**
     * 订单批量支付
     *
     * @param req
     */
    void batchPay(PurchaseOrderPayReq req);

    /**
     * 批量完成
     *
     * @param idsReq
     */
    void batchFinish(IdsReq idsReq);

    /**
     * 采购核对
     *
     * @param createTime
     * @return
     */
    List<PurchaseOrderCheckResp> check(LocalDateTime[] createTime);

    /**
     * 验收
     *
     * @param req
     */
    void receive(PurchaseOrderReceiveReq req);
}