package top.continew.admin.biz.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.YearMonth;

@Data
public class CustomerPerformanceInfoReq {

    @Schema(description = "类型 1-首充新客户 2-(3000,1w] 3-(1w-3w] 4-(3w,5w]")
    @NotNull(message = "类型不能为空")
    private Integer type;

    @Schema(description = "商务人员ID")
    @NotNull(message = "商务人员ID不能为空")
    private Long businessUserId;

    @Schema(description = "月份")
    @NotNull(message = "月份不能为空")
    private YearMonth yearMonth;
}
