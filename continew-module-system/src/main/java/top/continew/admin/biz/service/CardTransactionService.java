/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.model.entity.CardTransactionDO;
import top.continew.admin.biz.model.query.CardTransactionQuery;
import top.continew.admin.biz.model.query.CardTransactionStatisticsQuery;
import top.continew.admin.biz.model.req.CardTransactionRemarkReq;
import top.continew.admin.biz.model.req.CardTransactionReq;
import top.continew.admin.biz.model.resp.*;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 卡片交易流水业务接口
 *
 * <AUTHOR>
 * @since 2024/12/31 21:57
 */
public interface CardTransactionService extends BaseService<CardTransactionResp, CardTransactionDetailResp, CardTransactionQuery, CardTransactionReq>, IService<CardTransactionDO> {

    List<CardTransactionDO> listByPlatform(Integer platform, LocalDateTime start, LocalDateTime end);

    /**
     * 同步流水数据
     *
     * @param start
     * @param end
     */
    void syncData(LocalDateTime start, LocalDateTime end);

    /**
     * 同步流水数据
     *
     * @param platform
     * @param start
     * @param end
     */
    void syncData(CardPlatformEnum platform, LocalDateTime start, LocalDateTime end);

    /**
     * 备注
     *
     * @param id
     * @param req
     */
    void remark(Long id, CardTransactionRemarkReq req);

    /**
     * 统计指定时间范围内的卡台交易金额
     *
     * @param platformAdId 广告户ID
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return 交易总金额
     */
    BigDecimal sumAmountByDateRange(String platformAdId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 同步清零流水数据
     */
    void syncClearTransactionData();

    /**
     * 转换卡片交易时间
     */
    void convertCardTransactionTime();

    /**
     * cv回调
     *
     * @param data
     */
    void syncCardVpByCallback(JSONObject data);

    void updateCardNumber(CardPlatformEnum platform);

    void handleGzyTransaction(JSONArray jsonArray);

    void handleItlTransaction(JSONArray jsonArray);

    /**
     * 处理AMZ交易
     *
     * @param jsonArray
     */
    void handleAmzTransaction(JSONArray jsonArray);

    /**
     * 更新广告账户ID（指定时间范围）
     */
    void updateAdAccountId(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 更新客户ID（指定时间范围）
     */
    void updateCustomerId(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 交易流水统计
     *
     * @param query 统计查询参数
     * @return 统计结果
     */
    CardTransactionSummaryResp statistics(CardTransactionStatisticsQuery query);

    /**
     * 持卡人统计
     *
     * @param query
     * @param pageQuery
     * @return
     */
    PageResp<CardTransactionStatByCardholderResp> selectStatByCardholderPage(CardTransactionStatisticsQuery query,
                                                                             PageQuery pageQuery);

    /**
     * 日期统计
     *
     * @param query
     * @param pageQuery
     * @return
     */
    PageResp<CardTransactionStatByDateResp> selectStatByDatePage(CardTransactionStatisticsQuery query,
                                                                 PageQuery pageQuery);

    List<CardTransactionStatByDateResp> selectStatByDateList(CardTransactionStatisticsQuery query);

    /**
     * 时区统计
     *
     * @param query
     * @param pageQuery
     * @return
     */
    PageResp<CardTransactionStatByTimezoneResp> selectStatByTimezonePage(CardTransactionStatisticsQuery query,
                                                                         PageQuery pageQuery);

    /**
     * 获取卡片卡台消耗
     *
     * @param customerId
     * @param platformAdId
     * @param startTime
     * @return
     */
    BigDecimal getAdAccountCardSpent(Long customerId, String platformAdId, LocalDateTime startTime);

}