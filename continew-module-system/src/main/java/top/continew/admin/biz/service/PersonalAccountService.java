package top.continew.admin.biz.service;

import org.springframework.web.multipart.MultipartFile;
import top.continew.admin.biz.enums.PersonalAccoutTypeEnum;
import top.continew.admin.biz.model.entity.PersonalAccountDO;
import top.continew.admin.biz.model.query.PersonalAccountQuery;
import top.continew.admin.biz.model.req.PersonAccountBatchUpdateReq;
import top.continew.admin.biz.model.req.PersonalAccountReq;
import top.continew.admin.biz.model.resp.PersonalAccountDetailResp;
import top.continew.admin.biz.model.resp.PersonalAccountResp;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.model.resp.LabelValueResp;
import top.continew.starter.extension.crud.service.BaseService;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 个号业务接口
 *
 * <AUTHOR>
 * @since 2025/02/27 14:48
 */
public interface PersonalAccountService extends BaseService<PersonalAccountResp, PersonalAccountDetailResp, PersonalAccountQuery, PersonalAccountReq>, IService<PersonalAccountDO> {

    /**
     * 获取渠道列表
     *
     * @return
     */
    List<LabelValueResp<String>> getChannelList();

    /**
     * 导入数据
     *
     * @param file
     * @param channelId
     * @param unitPrice
     * @param purchaseTime
     */
    void importExcel(MultipartFile file, Long channelId, BigDecimal unitPrice, LocalDate purchaseTime, Boolean isAfterSale, PersonalAccoutTypeEnum type);

    /**
     * 接入
     *
     * @param id
     */
    void access(Long id);

    /**
     * 批量修改
     *
     * @param req
     */
    void batchUpdate(PersonAccountBatchUpdateReq req);
}