/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.telegram.telegrambots.meta.api.methods.ParseMode;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import top.continew.admin.biz.config.TelegramChatIdConfig;
import top.continew.admin.biz.enums.AdAccountUnusableReasonEnum;
import top.continew.admin.biz.enums.BusinessManagerBannedReasonEnum;
import top.continew.admin.biz.enums.BusinessManagerStatusEnum;
import top.continew.admin.biz.enums.BusinessManagerTypeEnum;
import top.continew.admin.biz.event.BMAddUserEvent;
import top.continew.admin.biz.event.BMDisabledEvent;
import top.continew.admin.biz.event.TelegramMessageEvent;
import top.continew.admin.biz.excel.BusinessManagerExcelData;
import top.continew.admin.biz.mapper.BusinessManagerMapper;
import top.continew.admin.biz.model.entity.*;
import top.continew.admin.biz.model.query.BusinessManagerQuery;
import top.continew.admin.biz.model.req.BusinessManagerReq;
import top.continew.admin.biz.model.resp.BusinessManagerDetailResp;
import top.continew.admin.biz.model.resp.BusinessManagerResp;
import top.continew.admin.biz.robot.utils.BotUtils;
import top.continew.admin.biz.service.*;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.admin.biz.utils.RabbitUtils;
import top.continew.admin.common.context.UserContextHolder;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.data.mp.util.QueryWrapperHelper;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.LabelValueResp;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.starter.file.excel.util.ExcelUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * BM5账号业务实现
 *
 * <AUTHOR>
 * @since 2024/12/30 17:48
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BusinessManagerServiceImpl extends BaseServiceImpl<BusinessManagerMapper, BusinessManagerDO, BusinessManagerResp, BusinessManagerDetailResp, BusinessManagerQuery, BusinessManagerReq> implements BusinessManagerService {

    private final BusinessManagerChannelService businessManagerChannelService;

    private final AdAccountService adAccountService;

    private final BusinessManagerItemService businessManagerItemService;

    private final BusinessManagerUserService businessManagerUserService;

    private final TelegramChatIdConfig telegramChatIdConfig;

    @Override
    public PageResp<BusinessManagerResp> page(BusinessManagerQuery query, PageQuery pageQuery) {
        QueryWrapper<BusinessManagerDO> queryWrapper = this.buildQueryWrapper(query);
        this.sort(queryWrapper, pageQuery);
        if (query.getDrop() != null) {
            String dropConditionSql = "SELECT 1 FROM biz_ad_account ad WHERE ad.business_manager_id = bm.id AND ad.real_adtrust_dsl = 50";
            if (query.getDrop()) {
                queryWrapper.exists(dropConditionSql);
            } else {
                queryWrapper.notExists(dropConditionSql);
            }
        }
        IPage<BusinessManagerResp> page = baseMapper.selectCustomPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), queryWrapper);
        PageResp<BusinessManagerResp> pageResp = PageResp.build(page, this.getListClass());
        pageResp.getList().forEach(this::fill);
        return pageResp;
    }

    @Override
    protected void sort(QueryWrapper<BusinessManagerDO> queryWrapper, SortQuery sortQuery) {
        if (sortQuery != null && !sortQuery.getSort().isUnsorted()) {
            Sort sort = sortQuery.getSort();
            Iterator var4 = sort.iterator();

            while (var4.hasNext()) {
                Sort.Order order = (Sort.Order)var4.next();
                String property = order.getProperty();
                queryWrapper.orderBy(true, order.isAscending(), CharSequenceUtil.toUnderlineCase(property));
            }

        }
    }

    @Override
    public void export(BusinessManagerQuery query, SortQuery sortQuery, HttpServletResponse response) {
        List<BusinessManagerResp> list = baseMapper.selectCustomList(this.buildQueryWrapper(query));
        list.forEach(this::fill);
        List<BusinessManagerDetailResp> result = new ArrayList<>();
        List<BusinessManagerChannelDO> channelList = businessManagerChannelService.list();
        Map<Long, String> nameMap = channelList.stream()
            .collect(Collectors.toMap(BusinessManagerChannelDO::getId, BusinessManagerChannelDO::getName));
        for (BusinessManagerResp businessManagerResp : list) {
            BusinessManagerDetailResp businessManagerDetailResp = BeanUtil.copyProperties(businessManagerResp, BusinessManagerDetailResp.class);
            businessManagerDetailResp.setChannelName(nameMap.get(businessManagerDetailResp.getChannelId()));
            result.add(businessManagerDetailResp);
        }
        ExcelUtils.export(result, "导出数据", this.getDetailClass(), response);
    }

    @Override
    protected QueryWrapper<BusinessManagerDO> buildQueryWrapper(BusinessManagerQuery query) {
        QueryWrapper<BusinessManagerDO> queryWrapper = new QueryWrapper<>();
        QueryWrapperHelper.build(query, this.getQueryFields(), queryWrapper);
        Map<Function<BusinessManagerQuery, String>, String> fieldToColumnMap = new LinkedHashMap<>();
        fieldToColumnMap.put(BusinessManagerQuery::getRemark, "remark");
        fieldToColumnMap.put(BusinessManagerQuery::getOpsBrowser, "ops_browser");
        fieldToColumnMap.put(BusinessManagerQuery::getReserveBrowser, "reserve_browser");
        fieldToColumnMap.put(BusinessManagerQuery::getReserveBrowserBak, "reserve_browser_bak");
        fieldToColumnMap.put(BusinessManagerQuery::getObserveBrowser, "observe_browser");

        fieldToColumnMap.forEach((getter, column) -> {
            String value = getter.apply(query);
            if (StrUtil.isNotBlank(value)) {
                if ("空".equals(value)) {
                    queryWrapper.eq(column, StrUtil.EMPTY);
                } else {
                    queryWrapper.like(column, value);
                }
            }
        });
        return queryWrapper;
    }

    @Override
    public List<BusinessManagerResp> list(BusinessManagerQuery query, SortQuery sortQuery) {
        QueryWrapper<BusinessManagerDO> queryWrapper = this.buildQueryWrapper(query);
        this.sort(queryWrapper, sortQuery);
        return baseMapper.selectCustomList(queryWrapper);
    }

    @Override
    protected void beforeAdd(BusinessManagerReq req) {
        if (exists(new LambdaQueryWrapper<BusinessManagerDO>().eq(BusinessManagerDO::getPlatformId, req.getPlatformId()))) {
            throw new BusinessException("BMID已存在");
        }
        if (req.getType().equals(BusinessManagerTypeEnum.BM5.getLongValue()) && req.getNum() == null) {
            throw new BusinessException("BM5账号请填写坑位");
        }
        this.addOrderUpdateCheck(req);
    }

    @Override
    protected void beforeUpdate(BusinessManagerReq req, Long id) {
        this.addOrderUpdateCheck(req);
    }

    private void addOrderUpdateCheck(BusinessManagerReq req) {
        CheckUtils.throwIf(req.getIsUse() && req.getUseTime() == null, "请填写使用时间");
        CheckUtils.throwIf(req.getIsUse() && StringUtils.isBlank(req.getUser()), "请填写使用者");
        if (!req.getIsUse()) {
            req.setUser("");
            req.setUseTime(null);
        }
        CheckUtils.throwIf(CommonUtils.containsNonDigit(req.getPlatformId()), "bm id包含非法字符，请检查");
        req.setPlatformId(StringUtils.trim(req.getPlatformId()));
    }

    @Override
    public void update(BusinessManagerReq req, Long id) {
        this.beforeUpdate(req, id);
        BusinessManagerDO entity = this.getById(id);
        // 同步广告户不可用原因
        if (req.getStatus().equals(BusinessManagerStatusEnum.BANNED) && !entity.getType()
            .equals(BusinessManagerTypeEnum.BM1.getLongValue())) {
            if (!req.getBannedReason().equals(entity.getBannedReason())) {
                LocalDateTime afterBanTime = LocalDateTime.now().minusHours(6);
                AdAccountUnusableReasonEnum adAccountUnusableReason = AdAccountUnusableReasonEnum.getByBmBanReason(req.getBannedReason());
                adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
                    .set(AdAccountDO::getUnusableReason, adAccountUnusableReason)
                    .eq(AdAccountDO::getBusinessManagerId, entity.getId())
                    .gt(AdAccountDO::getBanTime, afterBanTime));
            }
        }
        BeanUtil.copyProperties(req, entity, CopyOptions.create().ignoreNullValue());
        this.baseMapper.updateById(entity);
        this.afterUpdate(req, entity);
    }

    @Override
    protected void afterUpdate(BusinessManagerReq req, BusinessManagerDO entity) {
        businessManagerItemService.updatePrice(entity);
    }

    @Override
    protected void afterAdd(BusinessManagerReq req, BusinessManagerDO entity) {
        if (req.getNum() != null) {
            businessManagerItemService.addBusinessManagerItem(req, entity);
        }
    }

    @Override
    public void importExcel(MultipartFile file) {
        List<BusinessManagerExcelData> importRowList;
        try {
            importRowList = EasyExcel.read(file.getInputStream())
                .head(BusinessManagerExcelData.class)
                .sheet("BM5")
                .doReadSync();
        } catch (IOException e) {
            log.error("用户导入数据文件解析异常：{}", e.getMessage(), e);
            throw new BusinessException("数据文件解析异常");
        }
        Map<String, List<BusinessManagerExcelData>> map = importRowList.stream()
            .filter(v -> StringUtils.isNotBlank(v.getChannel()))
            .collect(Collectors.groupingBy(BusinessManagerExcelData::getChannel));
        List<BusinessManagerDO> saveList = new ArrayList<>();
        map.forEach((channel, dataList) -> {
            BusinessManagerChannelDO businessManagerChannelDO = new BusinessManagerChannelDO();
            businessManagerChannelDO.setName(channel);
            businessManagerChannelService.save(businessManagerChannelDO);
            for (BusinessManagerExcelData businessManagerExcelData : dataList) {
                if (businessManagerExcelData == null) {
                    continue;
                }
                if (StringUtils.isBlank(businessManagerExcelData.getAccountInfo())) {
                    continue;
                }
                BusinessManagerDO businessManagerDO = new BusinessManagerDO();
                businessManagerDO.setChannelId(businessManagerChannelDO.getId());
                businessManagerDO.setContent(businessManagerExcelData.getAccountInfo());
                businessManagerDO.setPlatformId(getBm5Id(businessManagerExcelData.getAccountInfo()));
                businessManagerDO.setBrowserNo(businessManagerExcelData.getBrowser());
                businessManagerDO.setCreateTime(LocalDateTimeUtil.parse(businessManagerExcelData.getPurchaseDate(), "yyyy-MM-dd"));
                businessManagerDO.setStatus("已封禁".equals(businessManagerExcelData.getStatus())
                    ? BusinessManagerStatusEnum.BANNED
                    : BusinessManagerStatusEnum.NORMAL);
                boolean isUse = true;
                if (StringUtils.isBlank(businessManagerExcelData.getStatus()) || "未使用".equals(businessManagerExcelData.getStatus())) {
                    isUse = false;
                }
                if ("已封禁".equals(businessManagerExcelData.getStatus()) && StringUtils.isBlank(businessManagerExcelData.getUser())) {
                    isUse = false;
                }
                businessManagerDO.setIsUse(isUse);
                businessManagerDO.setRemark(businessManagerExcelData.getRemark());
                saveList.add(businessManagerDO);
            }
        });
        saveBatch(saveList);

    }

    @Override
    public void used(Long id) {
        BusinessManagerDO businessManagerDO = new BusinessManagerDO();
        businessManagerDO.setId(id);
        businessManagerDO.setIsUse(true);
        businessManagerDO.setUseTime(LocalDateTime.now());
        businessManagerDO.setUser(UserContextHolder.getNickname());
        this.updateById(businessManagerDO);
    }

    @Override
    public void addNum(String bmId) {
        this.update(Wrappers.<BusinessManagerDO>lambdaUpdate()
            .setSql("use_num = use_num + 1")
            .eq(BusinessManagerDO::getPlatformId, bmId));
    }

    @Override
    public void ban(Long id, BusinessManagerBannedReasonEnum reason) {
        BusinessManagerDO businessManager = this.getById(id);
        if (businessManager.getStatus().equals(BusinessManagerStatusEnum.BANNED)) {
            return;
        }
        BMDisabledEvent event = new BMDisabledEvent(businessManager.getPlatformId(), reason);
        SpringUtil.publishEvent(event);
    }

    @Override
    public void normal(Long id) {
        BusinessManagerDO businessManager = new BusinessManagerDO();
        businessManager.setId(id);
        businessManager.setStatus(BusinessManagerStatusEnum.NORMAL);
        this.updateById(businessManager);
    }

    @Override
    public void calcAdAccountCost(LocalDate statDate) {
        LocalDateTime startTime = statDate.atStartOfDay();
        LocalDateTime endTime = statDate.plusDays(1).atStartOfDay();
        // 3 当天接入坑位 = 当天已使用数 + 当天未使用封禁数
        List<BusinessManagerItemDO> usedItems = businessManagerItemService.list(new LambdaQueryWrapper<BusinessManagerItemDO>().eq(BusinessManagerItemDO::getIsUse, true)
            .between(BusinessManagerItemDO::getUseTime, startTime, endTime));
        List<BusinessManagerItemDO> unusedBanneItems = businessManagerItemService.list(new LambdaQueryWrapper<BusinessManagerItemDO>().between(BusinessManagerItemDO::getBanTime, startTime, endTime)
            .eq(BusinessManagerItemDO::getIsUse, false));

        // 5. 已使用正常（使用时间）
        long usedNormal = usedItems.stream()
            .filter(v -> v.getStatus().equals(BusinessManagerStatusEnum.NORMAL))
            .count();

        if (usedNormal == 0) {
            return;
        }

        // 4 总成本=当天接入坑位数*单价
        BigDecimal totalCost = usedItems.stream()
            .map(BusinessManagerItemDO::getUnitPrice)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        totalCost = totalCost.add(unusedBanneItems.stream()
            .map(BusinessManagerItemDO::getUnitPrice)
            .reduce(BigDecimal.ZERO, BigDecimal::add));
        BigDecimal averagePrepareCost = totalCost.divide(BigDecimal.valueOf(usedNormal), 2, RoundingMode.HALF_UP);
        List<String> platformAdIdList = usedItems.stream()
            .filter(v -> v.getStatus().equals(BusinessManagerStatusEnum.NORMAL))
            .map(BusinessManagerItemDO::getPlatformAdId)
            .filter(StringUtils::isNotBlank)
            .toList();
        if (platformAdIdList.isEmpty()) {
            return;
        }
        adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .set(AdAccountDO::getCost, averagePrepareCost)
            .in(AdAccountDO::getPlatformAdId, platformAdIdList));
    }

    @Override
    public void syncData(String envId, String serialNumber, String proxy, String headers) {
        String response;
        if (StringUtils.isNotBlank(serialNumber)) {
            response = RabbitUtils.getAllBusinessManagersV2(serialNumber, proxy, headers);
        } else {
            response = RabbitUtils.getAllBusinessManagers(envId);
        }
        String browserKey = StringUtils.defaultIfBlank(envId, serialNumber);
        JSONObject jsonObject = JSONObject.parseObject(response);
        int code = jsonObject.getInteger("code");
        if (code != 0) {
            log.error("【{}】BM数据同步失败：{}", browserKey, jsonObject.getString("msg"));
            return;
        }
        List<BusinessManagerDO> bmList = this.list(Wrappers.<BusinessManagerDO>lambdaQuery()
            .eq(BusinessManagerDO::getSyncBrowserNo, serialNumber));
        JSONArray data = jsonObject.getJSONArray("data");
        List<BusinessManagerUserDO> saveList = new ArrayList<>();
        List<BusinessManagerUserDO> updateList = new ArrayList<>();
        List<String> currentBmList = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            JSONObject item = data.getJSONObject(i);
            String bmId = item.getString("id");
            currentBmList.add(bmId);
            this.update(Wrappers.<BusinessManagerDO>lambdaUpdate()
                .set(BusinessManagerDO::getSyncBrowserNo, serialNumber)
                .eq(BusinessManagerDO::getPlatformId, bmId));
            boolean isDisabled = item.getBoolean("is_disabled_for_integrity_reasons");
            if (isDisabled) {
                BMDisabledEvent event = new BMDisabledEvent(bmId, BusinessManagerBannedReasonEnum.BANNED_SYSTEM);
                SpringUtil.publishEvent(event);
            } else {
                this.update(Wrappers.<BusinessManagerDO>lambdaUpdate()
                    .set(BusinessManagerDO::getStatus, BusinessManagerStatusEnum.NORMAL)
                    .set(BusinessManagerDO::getBanTime, null)
                    .set(BusinessManagerDO::getBannedReason, null)
                    .eq(BusinessManagerDO::getPlatformId, bmId));
                // 更新管理员
                List<BusinessManagerUserDO> userList = businessManagerUserService.list(Wrappers.<BusinessManagerUserDO>lambdaQuery()
                    .eq(BusinessManagerUserDO::getBmId, bmId));
                List<String> currentUserIds = new ArrayList<>();
                JSONArray users = item.getJSONArray("user");
                if (users == null) {
                    log.info("【{}】BM ID {} 管理员列表为空", serialNumber, bmId);
                    users = new JSONArray();
                }
                for (int i1 = 0; i1 < users.size(); i1++) {
                    JSONObject user = users.getJSONObject(i1);
                    String userId = user.getString("id");
                    currentUserIds.add(userId);
                    BusinessManagerUserDO exist = userList.stream()
                        .filter(v -> v.getUserId().equals(userId))
                        .findFirst()
                        .orElse(null);
                    if (exist == null) {
                        BusinessManagerUserDO saveItem = new BusinessManagerUserDO();
                        saveItem.setBmId(bmId);
                        saveItem.setUserId(userId);
                        saveItem.setUsername(user.getString("name"));
                        saveItem.setUserEmail(user.getString("email"));
                        saveItem.setUserRole(user.getString("role"));
                        saveItem.setIsRemove(false);
                        saveList.add(saveItem);
                        if (!userList.isEmpty() && "ADMIN".equals(saveItem.getUserRole())) {
                            BMAddUserEvent event = new BMAddUserEvent(saveItem);
                            SpringUtil.publishEvent(event);
                        }
                    } else {
                        BusinessManagerUserDO updateItem = new BusinessManagerUserDO();
                        updateItem.setId(exist.getId());
                        updateItem.setUsername(user.getString("name"));
                        updateItem.setUserEmail(user.getString("email"));
                        updateItem.setUserRole(user.getString("role"));
                        updateItem.setIsRemove(false);
                        updateList.add(updateItem);
                        if (exist.getIsRemove()) {
                            if ("ADMIN".equals(updateItem.getUserRole())) {
                                BMAddUserEvent event = new BMAddUserEvent(exist);
                                SpringUtil.publishEvent(event);
                            }
                        }
                    }
                }
                for (BusinessManagerUserDO businessManagerUserDO : userList) {
                    if (!currentUserIds.contains(businessManagerUserDO.getUserId())) {
                        BusinessManagerUserDO update = new BusinessManagerUserDO();
                        update.setId(businessManagerUserDO.getId());
                        update.setIsRemove(true);
                        updateList.add(update);
                    }
                }
            }

        }
        businessManagerUserService.saveBatch(saveList);
        businessManagerUserService.updateBatchById(updateList);

        // 检测bm是否被移除
        for (BusinessManagerDO businessManagerDO : bmList) {
            if (!currentBmList.contains(businessManagerDO.getPlatformId())) {
                this.update(Wrappers.<BusinessManagerDO>lambdaUpdate()
                    .set(BusinessManagerDO::getSyncBrowserNo, "")
                    .eq(BusinessManagerDO::getId, businessManagerDO.getId()));
                BusinessManagerChannelDO channel = businessManagerChannelService.getById(businessManagerDO.getChannelId());
                log.info("【{}】检测到BMID {}被移除", serialNumber, businessManagerDO.getPlatformId());
                String text = BotUtils.createBMPermissionLostMessage(businessManagerDO.getPlatformId(), channel.getName(), LocalDateTimeUtil.format(businessManagerDO.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                    .chatId(telegramChatIdConfig.getMonitorChatId())
                    .text(text)
                    .parseMode(ParseMode.MARKDOWNV2)
                    .build()));
                SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                    .chatId(telegramChatIdConfig.getFinanceChatId())
                    .text(text)
                    .parseMode(ParseMode.MARKDOWNV2)
                    .build()));
                if (businessManagerDO.getStatus().equals(BusinessManagerStatusEnum.NORMAL)) {
                    BMDisabledEvent event = new BMDisabledEvent(businessManagerDO.getPlatformId(), BusinessManagerBannedReasonEnum.BANNED_BY_KILL);
                    SpringUtil.publishEvent(event);
                }
            }
        }
    }

    @Override
    protected void afterDelete(List<Long> ids) {
        businessManagerItemService.remove(Wrappers.<BusinessManagerItemDO>lambdaQuery()
            .in(BusinessManagerItemDO::getBusinessManagerId, ids));
    }

    @Override
    public List<LabelValueResp> listBM1() {
        List<LabelValueResp> list = new ArrayList<>();
        List<BusinessManagerDO> bmList = this.list(Wrappers.<BusinessManagerDO>lambdaQuery()
            .eq(BusinessManagerDO::getStatus, BusinessManagerStatusEnum.NORMAL)
            .in(BusinessManagerDO::getType, List.of(7L, 747488281653936135L)));
        for (BusinessManagerDO businessManagerDO : bmList) {
            LabelValueResp labelValueResp = new LabelValueResp(businessManagerDO.getPlatformId(), businessManagerDO.getId());
            list.add(labelValueResp);
        }
        return list;
    }

    private String getBm5Id(String accountInfo) {
        String[] s = accountInfo.split("\\|");
        if (s.length == 1) {
            return accountInfo.split(" ")[0];
        } else {
            return s[0];
        }
    }
}