package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;
import java.math.BigDecimal;

import cn.hutool.core.date.DatePattern;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.springframework.format.annotation.DateTimeFormat;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.annotation.QueryIgnore;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 利润查询条件
 *
 * <AUTHOR>
 * @since 2025/08/16 16:14
 */
@Data
@Schema(description = "利润查询条件")
public class ProfitQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 媒体平台
     */
    @Schema(description = "媒体平台")
    @Query(type = QueryType.EQ, columns = "p.ad_platform")
    private Integer adPlatform;

    /**
     * 项目
     */
    @Schema(description = "项目")
    @Query(type = QueryType.EQ, columns = "p.project")
    private Integer project;

    /**
     * 类型
     */
    @Schema(description = "类型")
    @Query(type = QueryType.EQ, columns = "p.type")
    private Long type;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间")
    @Query(type = QueryType.BETWEEN, columns = "p.trans_time")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] transTime;

    /**
     * 交易哈希
     */
    @Schema(description = "交易哈希")
    @Query(type = QueryType.EQ, columns = "p.transaction_hash")
    private String transactionHash;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Query(type = QueryType.LIKE, columns = "p.remark")
    private String remark;

    @QueryIgnore
    private Integer action;
}