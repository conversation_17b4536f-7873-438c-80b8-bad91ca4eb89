package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.SettleOrderDO;
import top.continew.admin.biz.model.query.SettleOrderQuery;
import top.continew.admin.biz.model.req.SettleOrderReq;
import top.continew.admin.biz.model.resp.SettleOrderDetailResp;
import top.continew.admin.biz.model.resp.SettleOrderResp;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;

/**
 * 结算订单业务接口
 *
 * <AUTHOR>
 * @since 2025/03/20 14:56
 */
public interface SettleOrderService extends BaseService<SettleOrderResp, SettleOrderDetailResp, SettleOrderQuery, SettleOrderReq>, IService<SettleOrderDO> {

}