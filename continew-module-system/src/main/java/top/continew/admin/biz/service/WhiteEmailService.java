package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.WhiteEmailDO;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.WhiteEmailQuery;
import top.continew.admin.biz.model.req.WhiteEmailReq;
import top.continew.admin.biz.model.resp.WhiteEmailDetailResp;
import top.continew.admin.biz.model.resp.WhiteEmailResp;

import java.util.List;

/**
 * 白名单邮箱业务接口
 *
 * <AUTHOR>
 * @since 2025/07/01 14:36
 */
public interface WhiteEmailService extends BaseService<WhiteEmailResp, WhiteEmailDetailResp, WhiteEmailQuery, WhiteEmailReq>, IService<WhiteEmailDO> {

    void saveByEmail(List<String> emails);
}