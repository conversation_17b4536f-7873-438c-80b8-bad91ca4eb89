package top.continew.admin.biz.listener;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.meta.api.methods.ParseMode;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import top.continew.admin.biz.config.TelegramChatIdConfig;
import top.continew.admin.biz.enums.*;
import top.continew.admin.biz.event.AdAccountStatusChangeEvent;
import top.continew.admin.biz.event.BMAddUserEvent;
import top.continew.admin.biz.event.BMDisabledEvent;
import top.continew.admin.biz.event.TelegramMessageEvent;
import top.continew.admin.biz.model.entity.*;
import top.continew.admin.biz.robot.utils.BotUtils;
import top.continew.admin.biz.service.*;

import java.time.LocalDateTime;
import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class BmListener {

    private final BusinessManagerService businessManagerService;

    private final BusinessManagerItemService businessManagerItemService;

    private final BusinessManagerChannelService businessManagerChannelService;

    private final AdAccountService adAccountService;

    private final AdAccountOrderService adAccountOrderService;

    private final TelegramChatIdConfig telegramChatIdConfig;

    private final WhiteEmailService whiteEmailService;

    @EventListener
    public void create(BMDisabledEvent event) {
        String bmId = (String)event.getSource();
        List<BusinessManagerDO> bmList = businessManagerService.list(Wrappers.<BusinessManagerDO>lambdaQuery()
            .eq(BusinessManagerDO::getPlatformId, bmId));
        if (bmList.isEmpty()) {
            return;
        }

        for (BusinessManagerDO businessManagerDO : bmList) {
            boolean flag = businessManagerService.update(Wrappers.<BusinessManagerDO>lambdaUpdate()
                .set(BusinessManagerDO::getStatus, BusinessManagerStatusEnum.BANNED)
                .set(BusinessManagerDO::getBanTime, LocalDateTime.now())
                .set(null != event.getReason(), BusinessManagerDO::getBannedReason, event.getReason())
                .eq(BusinessManagerDO::getStatus, BusinessManagerStatusEnum.NORMAL)
                .eq(BusinessManagerDO::getId, businessManagerDO.getId()));
            if (flag) {
                log.info("BM {}已被封禁", bmId);
                AdAccountUnusableReasonEnum adAccountUnusableReason = AdAccountUnusableReasonEnum.getByBmBanReason(event.getReason());

                if (businessManagerDO.getType().equals(BusinessManagerTypeEnum.BM1.getLongValue())) {
                    // bm1 仅需要封禁认领户
                    List<BusinessManagerItemDO> items = businessManagerItemService.list(Wrappers.<BusinessManagerItemDO>lambdaQuery()
                        .eq(BusinessManagerItemDO::getBusinessManagerId, businessManagerDO.getId())
                        .eq(BusinessManagerItemDO::getOwnMethod, BusinessManagerOwnMethodEnum.CLAIM.getValue())
                        .eq(BusinessManagerItemDO::getStatus, BusinessManagerStatusEnum.NORMAL));
                    for (BusinessManagerItemDO item : items) {
                        businessManagerItemService.update(new LambdaUpdateWrapper<BusinessManagerItemDO>().set(BusinessManagerItemDO::getStatus, BusinessManagerStatusEnum.BANNED)
                            .set(BusinessManagerItemDO::getBanTime, LocalDateTime.now())
                            .eq(BusinessManagerItemDO::getId, item.getId()));
                        if (StringUtils.isNotBlank(item.getPlatformAdId())) {
                            AdAccountDO statusChange = new AdAccountDO();
                            statusChange.setPlatformAdId(item.getPlatformAdId());
                            statusChange.setAccountStatus(AdAccountStatusEnum.BANNED);
                            statusChange.setUsable(false);
                            statusChange.setUnusableReason(adAccountUnusableReason);
                            AdAccountStatusChangeEvent adAccountStatusChangeEvent = new AdAccountStatusChangeEvent(statusChange);
                            SpringUtil.publishEvent(adAccountStatusChangeEvent);
                        }
                    }
                } else {
                    // 直接封禁下面所有的户
                    businessManagerItemService.banBusinessManager(businessManagerDO.getId());

                    // 批量修改户的封禁状态，有可能是户先检测到封禁，后面bm才检测到权限移除导致广告户不可用原因不准确
                    LocalDateTime afterBanTime = LocalDateTime.now().minusHours(6);
                    adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
                        .set(AdAccountDO::getUnusableReason, adAccountUnusableReason)
                        .eq(AdAccountDO::getBusinessManagerId, businessManagerDO.getId())
                        .gt(AdAccountDO::getBanTime, afterBanTime));

                    List<AdAccountDO> adAccountList = adAccountService.list(Wrappers.<AdAccountDO>lambdaQuery()
                        .eq(AdAccountDO::getBusinessManagerId, businessManagerDO.getId())
                        .eq(AdAccountDO::getAccountStatus, AdAccountStatusEnum.NORMAL));

                    // ban广告户
                    for (AdAccountDO accountDO : adAccountList) {
                        AdAccountDO statusChange = new AdAccountDO();
                        statusChange.setPlatformAdId(accountDO.getPlatformAdId());
                        statusChange.setAccountStatus(AdAccountStatusEnum.BANNED);
                        statusChange.setUsable(false);
                        statusChange.setUnusableReason(adAccountUnusableReason);
                        AdAccountStatusChangeEvent adAccountStatusChangeEvent = new AdAccountStatusChangeEvent(statusChange);
                        SpringUtil.publishEvent(adAccountStatusChangeEvent);
                    }
                }
            }
        }
    }

    @EventListener
    @Async
    public void addUser(BMAddUserEvent event) {
        BusinessManagerUserDO user = (BusinessManagerUserDO)event.getSource();
        BusinessManagerDO businessManagerDO = businessManagerService.getOne(Wrappers.<BusinessManagerDO>lambdaQuery()
            .eq(BusinessManagerDO::getPlatformId, user.getBmId()));
        BusinessManagerChannelDO channelDO = businessManagerChannelService.getById(businessManagerDO.getChannelId());
        if (StringUtils.isBlank(user.getUserEmail())) {
            String text = BotUtils.createBMAdminAddMessage(user.getBmId(), user.getUsername(), "无", channelDO.getName(), LocalDateTimeUtil.format(businessManagerDO.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                .chatId(telegramChatIdConfig.getMonitorChatId())
                .text(text)
                .parseMode(ParseMode.MARKDOWNV2)
                .build()));
            BMDisabledEvent bmDisabledEvent = new BMDisabledEvent(user.getBmId(), BusinessManagerBannedReasonEnum.BANNED_BY_KILL);
            SpringUtil.publishEvent(bmDisabledEvent);
            return;
        }
        // 检测是否是自己的个号
        boolean isSelfAccount = whiteEmailService.exists(Wrappers.<WhiteEmailDO>lambdaQuery()
            .like(WhiteEmailDO::getEmail, user.getUserEmail()));
        if (isSelfAccount) {
            return;
        }
        // 检测是否是客户的个号
        boolean isCustomerAccount = adAccountOrderService.exists(Wrappers.<AdAccountOrderDO>lambdaQuery()
            .like(AdAccountOrderDO::getCustomerBmId, user.getUserEmail()));
        if (isCustomerAccount) {
            return;
        }
        String text = BotUtils.createBMAdminAddMessage(user.getBmId(), user.getUsername(), user.getUserEmail(), channelDO.getName(), LocalDateTimeUtil.format(businessManagerDO.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
        SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
            .chatId(telegramChatIdConfig.getMonitorChatId())
            .text(text)
            .parseMode(ParseMode.MARKDOWNV2)
            .build()));
        BMDisabledEvent bmDisabledEvent = new BMDisabledEvent(user.getBmId(), BusinessManagerBannedReasonEnum.BANNED_BY_KILL);
        SpringUtil.publishEvent(bmDisabledEvent);
    }
}
