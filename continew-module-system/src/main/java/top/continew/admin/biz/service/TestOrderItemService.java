package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.TestOrderItemDO;
import top.continew.admin.biz.model.resp.AdAccountTestOrderItemResp;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.TestOrderItemQuery;
import top.continew.admin.biz.model.req.TestOrderItemReq;
import top.continew.admin.biz.model.resp.TestOrderItemDetailResp;
import top.continew.admin.biz.model.resp.TestOrderItemResp;

import java.util.List;

/**
 * 测试任务详情业务接口
 *
 * <AUTHOR>
 * @since 2025/05/13 11:43
 */
public interface TestOrderItemService extends BaseService<TestOrderItemResp, TestOrderItemDetailResp, TestOrderItemQuery, TestOrderItemReq>, IService<TestOrderItemDO> {


    void batchUpdate(TestOrderItemReq req);

    List<AdAccountTestOrderItemResp> listByAdAccountId(String adAccountId);
}