/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.ClearOrderDO;
import top.continew.admin.biz.model.query.ClearAccountAnalyzeQuery;
import top.continew.admin.biz.model.query.ClearOrderQuery;
import top.continew.admin.biz.model.req.ClearOrderFinishReq;
import top.continew.admin.biz.model.req.ClearOrderReq;
import top.continew.admin.biz.model.resp.ClearAccountAnalyzeResp;
import top.continew.admin.biz.model.resp.ClearOrderDetailResp;
import top.continew.admin.biz.model.resp.ClearOrderResp;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseService;

import java.math.BigDecimal;

/**
 * 清零订单业务接口
 *
 * <AUTHOR>
 * @since 2024/12/31 11:17
 */
public interface ClearOrderService extends BaseService<ClearOrderResp, ClearOrderDetailResp, ClearOrderQuery, ClearOrderReq>, IService<ClearOrderDO> {

    /**
     * 处理订单
     *
     * @param id
     */
    void handleOrder(Long id);

    /**
     * 完成订单
     *
     * @param req
     */
    void finishOrder(ClearOrderFinishReq req);

    /**
     * 取消订单
     *
     * @param id
     */
    void cancelOrder(Long id);

    /**
     * 生成凭证图片
     *
     * @param id
     */
    void generateCertificate(Long id);

    /**
     * 清零检测
     *
     * @param platformAdId
     * @param fbOpsId
     */
    void checkModifyLimit(String platformAdId, String fbOpsId);

    /**
     * 上传截图凭证
     *
     * @param platformAdId
     * @param fbOpsId
     * @param imageBase64
     */
    void uploadModifyLimitCertificate(String platformAdId, String fbOpsId, String imageBase64);

    /**
     * 清零账户分析
     */
    PageResp<ClearAccountAnalyzeResp> getClearAccountAnalyze(ClearAccountAnalyzeQuery query);

    void clearApply(ClearOrderReq req);
}