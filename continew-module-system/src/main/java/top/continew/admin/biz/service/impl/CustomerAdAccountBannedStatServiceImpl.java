
package top.continew.admin.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.AdAccountOrderStatusEnum;
import top.continew.admin.biz.mapper.AdAccountOrderMapper;
import top.continew.admin.biz.mapper.analyze.CustomerAdAccountBannedStatMapper;
import top.continew.admin.biz.model.entity.AdAccountOrderDO;
import top.continew.admin.biz.model.query.BannedAnalyzeFactorQuery;
import top.continew.admin.biz.model.query.BannedAnalyzeQuery;
import top.continew.admin.biz.model.resp.CustomerBanStatsResp;
import top.continew.admin.biz.service.CustomerAdAccountBannedStatService;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.BasePageResp;
import top.continew.starter.extension.crud.model.resp.PageResp;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 客户广告户封禁分析业务接口
 *
 * <AUTHOR>
 * @since 2024/12/31 10:00
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerAdAccountBannedStatServiceImpl implements CustomerAdAccountBannedStatService {

    private final CustomerAdAccountBannedStatMapper customerAdAccountBannedStatMapper;

    private final AdAccountOrderMapper adAccountOrderMapper;


    @Override
    public BasePageResp<CustomerBanStatsResp> pageByCustomer(BannedAnalyzeQuery query, PageQuery pageQuery) {
        IPage<CustomerBanStatsResp> page = customerAdAccountBannedStatMapper.selectPageCustomerBase(new Page<>(pageQuery.getPage(), pageQuery.getSize()), query);

        for (CustomerBanStatsResp record : page.getRecords()) {
            CustomerBanStatsResp resp = customerAdAccountBannedStatMapper.selectInfoData(record.getCustomerId(), null, query.getStartTime(), query.getEndTime(), query.getBanType(),
                    null, null, null);
            record.setTotalCount(resp.getTotalCount());
            record.setTotalBanCount(resp.getTotalBanCount());
            record.setUnusedBanCount(resp.getUnusedBanCount());
            record.setUnconsumedBanCount(resp.getUnconsumedBanCount());
            record.setConsumedBanCount(resp.getConsumedBanCount());
        }
        return new PageResp<>(page.getRecords(), page.getTotal());
    }

    @Override
    public BasePageResp<CustomerBanStatsResp> pageByAdAccount(BannedAnalyzeQuery query, PageQuery pageQuery) {
        CheckUtils.throwIfBlank(query.getAdAccountIds(), "请输入广告户ID");
        String[] adAccountIds = query.getAdAccountIds().split("[\\s,，]+");
        List<AdAccountOrderDO> adAccountOrderList = adAccountOrderMapper.lambdaQuery()
                .in(AdAccountOrderDO::getAdAccountId, adAccountIds)
                .in(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED, AdAccountOrderStatusEnum.RECYCLE)
                .list();
        if (CollUtil.isEmpty(adAccountOrderList)) {
            return new PageResp<>(List.of(), 0);
        } else {
            Map<String, LocalDateTime> latestCreateTimeByAccountId = adAccountOrderList.stream().collect(
                    Collectors.toMap(
                            AdAccountOrderDO::getAdAccountId,
                            AdAccountOrderDO::getCreateTime,
                            (existingTime, newItemTime) -> newItemTime.isAfter(existingTime) ? newItemTime : existingTime
                    )
            );
            List<LocalDateTime> timeList = latestCreateTimeByAccountId.values().stream().toList();
            if (CollUtil.isEmpty(timeList)) {
                return new PageResp<>(List.of(), 0);
            }
            LocalDateTime[] adjustedTimeRange = CommonUtils.getAdjustedTimeRange(timeList);
            List<CustomerBanStatsResp> list = new ArrayList<>();
            CustomerBanStatsResp referenceData = customerAdAccountBannedStatMapper.selectInfoData(null, null, adjustedTimeRange[0], adjustedTimeRange[1], query.getBanType()
                    , null, null, null);
            referenceData.setCustomerName("参考数据");
            list.add(referenceData);
            // 要观察的户
            CustomerBanStatsResp targetData = customerAdAccountBannedStatMapper.selectInfoData(null, adAccountOrderList.stream().map(AdAccountOrderDO::getAdAccountId).distinct().toList(), null,
                    null, query.getBanType(), null, null, null);
            targetData.setCustomerName("要观察的户");
            list.add(targetData);
            return new PageResp<>(list, list.size());
        }
    }

    @Override
    public List<CustomerBanStatsResp> analyzeByCustomer(BannedAnalyzeFactorQuery query) {
        CheckUtils.throwIfNull(query.getCustomerId(), "客户ID不能为空");
        List<CustomerBanStatsResp> res = new ArrayList<>();

        Long customerId = query.getCustomerId();

        if (query.getOneKnifeFlow() != null && query.getOneKnifeFlow()) {
            CustomerBanStatsResp resp = customerAdAccountBannedStatMapper.selectInfoData(customerId, null, query.getStartTime(), query.getEndTime(), query.getBanType(),
                    true, null, null);
            resp.setCustomerName("一刀流");
            res.add(resp);
        }
        if (CollUtil.isNotEmpty(query.getCardHeader())) {
            query.getCardHeader().forEach(item -> {
                CustomerBanStatsResp resp = customerAdAccountBannedStatMapper.selectInfoData(customerId, null, query.getStartTime(), query.getEndTime(), query.getBanType(),
                        null, item, null);
                resp.setCustomerName(item);
                res.add(resp);
            });
        }
        if (CollUtil.isNotEmpty(query.getTimeZone())) {
            query.getTimeZone().forEach(item -> {
                CustomerBanStatsResp resp = customerAdAccountBannedStatMapper.selectInfoData(customerId, null, query.getStartTime(), query.getEndTime(), query.getBanType(),
                        null, null, item);
                resp.setCustomerName(item);
                res.add(resp);
            });
        }
        return res;
    }

    @Override
    public LocalDateTime[] timeInterval(String adAccountIds) {
        CheckUtils.throwIfBlank(adAccountIds, "请输入广告户ID");
        List<AdAccountOrderDO> list = adAccountOrderMapper.lambdaQuery()
                .in(AdAccountOrderDO::getAdAccountId, adAccountIds.split("[\\s,，]+"))
                .in(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED, AdAccountOrderStatusEnum.RECYCLE)
                .list();
        Map<String, LocalDateTime> latestCreateTimeByAccountId = list.stream().collect(
                Collectors.toMap(
                        AdAccountOrderDO::getAdAccountId,
                        AdAccountOrderDO::getCreateTime,
                        (existingTime, newItemTime) -> newItemTime.isAfter(existingTime) ? newItemTime : existingTime
                )
        );
        List<LocalDateTime> timeList = latestCreateTimeByAccountId.values().stream().toList();
        if (CollUtil.isEmpty(timeList)) {
            return new LocalDateTime[0];
        }
        return CommonUtils.getAdjustedTimeRange(timeList);
    }

    @Override
    public List<CustomerBanStatsResp> analyzeByAdAccountId(BannedAnalyzeFactorQuery query) {
        CheckUtils.throwIfBlank(query.getAdAccountIds(), "请输入广告户ID");
        String[] adAccountIds = query.getAdAccountIds().split("[\\s,，]+");
        List<AdAccountOrderDO> adAccountOrderList = adAccountOrderMapper.lambdaQuery()
                .in(AdAccountOrderDO::getAdAccountId, adAccountIds)
                .in(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED, AdAccountOrderStatusEnum.RECYCLE)
                .list();
        Map<String, LocalDateTime> latestCreateTimeByAccountId = adAccountOrderList.stream().collect(
                Collectors.toMap(
                        AdAccountOrderDO::getAdAccountId,
                        AdAccountOrderDO::getCreateTime,
                        (existingTime, newItemTime) -> newItemTime.isAfter(existingTime) ? newItemTime : existingTime
                )
        );
        List<LocalDateTime> timeList = latestCreateTimeByAccountId.values().stream().toList();
        List<CustomerBanStatsResp> list = new ArrayList<>();
        if (CollUtil.isEmpty(timeList)) {
            return list;
        } else {
            LocalDateTime[] adjustedTimeRange = CommonUtils.getAdjustedTimeRange(timeList);
            if (query.getOneKnifeFlow() != null && query.getOneKnifeFlow()) {
                CustomerBanStatsResp reference = customerAdAccountBannedStatMapper.selectInfoData(null, null, adjustedTimeRange[0], adjustedTimeRange[1], query.getBanType()
                        , true, null, null);
                reference.setCustomerName("一刀流");
                reference.setCustomerId(1L);
                list.add(reference);
                CustomerBanStatsResp target = customerAdAccountBannedStatMapper.selectInfoData(null, Arrays.asList(adAccountIds), adjustedTimeRange[0], adjustedTimeRange[1], query.getBanType()
                        , true, null, null);
                target.setCustomerName("一刀流");
                target.setCustomerId(2L);
                list.add(target);
            }
            if (CollUtil.isNotEmpty(query.getCardHeader())) {
                for (String card : query.getCardHeader()) {
                    CustomerBanStatsResp reference = customerAdAccountBannedStatMapper.selectInfoData(null, null, adjustedTimeRange[0], adjustedTimeRange[1], query.getBanType()
                            , null, card, null);
                    reference.setCustomerName(card);
                    reference.setCustomerId(1L);
                    list.add(reference);
                    CustomerBanStatsResp target = customerAdAccountBannedStatMapper.selectInfoData(null, Arrays.asList(adAccountIds), adjustedTimeRange[0], adjustedTimeRange[1], query.getBanType()
                            , null, card, null);
                    target.setCustomerName(card);
                    target.setCustomerId(2L);
                    list.add(target);
                }

            }
            if (CollUtil.isNotEmpty(query.getTimeZone())) {
                query.getTimeZone().forEach(item -> {
                    CustomerBanStatsResp reference = customerAdAccountBannedStatMapper.selectInfoData(null, null, adjustedTimeRange[0], adjustedTimeRange[1], query.getBanType()
                            , null, null, item);
                    reference.setCustomerName(item);
                    reference.setCustomerId(1L);
                    list.add(reference);
                    CustomerBanStatsResp target = customerAdAccountBannedStatMapper.selectInfoData(null, Arrays.asList(adAccountIds), adjustedTimeRange[0], adjustedTimeRange[1], query.getBanType()
                            , null, null, item);
                    target.setCustomerName(item);
                    target.setCustomerId(2L);
                    list.add(target);
                });
            }

        }
        return list;
    }

    @Override
    public CustomerBanStatsResp getCustomerDashboardStats(BannedAnalyzeQuery query) {
        return customerAdAccountBannedStatMapper.getCustomerDashboardStats(query.getCustomerIds(), query.getStartTime(), query.getEndTime(), query.getBanType());
    }

    @Override
    public CustomerBanStatsResp getAdAccountDashboardStats(BannedAnalyzeQuery query) {
        LocalDateTime[] localDateTimes = timeInterval(query.getAdAccountIds());

        return customerAdAccountBannedStatMapper.getAdAccountDashboardStats(localDateTimes[0], localDateTimes[1], query.getBanType());
    }
}