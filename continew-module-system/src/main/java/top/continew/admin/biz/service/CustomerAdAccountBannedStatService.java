/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service;

import top.continew.admin.biz.model.query.BannedAnalyzeFactorQuery;
import top.continew.admin.biz.model.query.BannedAnalyzeQuery;
import top.continew.admin.biz.model.query.CustomerCycleComparePeriodQuery;
import top.continew.admin.biz.model.query.CustomerCycleCompareSummaryQuery;
import top.continew.admin.biz.model.resp.CustomerBanStatsResp;
import top.continew.admin.biz.model.resp.CustomerCycleComparePeroidResp;
import top.continew.admin.biz.model.resp.CustomerCycleCompareSummaryResp;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.BasePageResp;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 客户广告户封禁分析业务接口
 *
 * <AUTHOR>
 * @since 2024/12/31 10:00
 */
public interface CustomerAdAccountBannedStatService {


    BasePageResp<CustomerBanStatsResp> pageByCustomer(BannedAnalyzeQuery query, PageQuery pageQuery);

    BasePageResp<CustomerBanStatsResp> pageByAdAccount(BannedAnalyzeQuery query, PageQuery pageQuery);

    List<CustomerBanStatsResp> analyzeByCustomer(BannedAnalyzeFactorQuery query);

    LocalDateTime[] timeInterval(String adAccountIds);

    List<CustomerBanStatsResp> analyzeByAdAccountId(BannedAnalyzeFactorQuery query);

    CustomerBanStatsResp getCustomerDashboardStats(BannedAnalyzeQuery query);

    CustomerBanStatsResp getAdAccountDashboardStats(BannedAnalyzeQuery query);
}