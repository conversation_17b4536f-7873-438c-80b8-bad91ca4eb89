package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.AdsPowerDO;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.AdsPowerQuery;
import top.continew.admin.biz.model.req.AdsPowerReq;
import top.continew.admin.biz.model.resp.AdsPowerDetailResp;
import top.continew.admin.biz.model.resp.AdsPowerResp;

/**
 * 指纹浏览器业务接口
 *
 * <AUTHOR>
 * @since 2025/04/25 17:15
 */
public interface AdsPowerService extends BaseService<AdsPowerResp, AdsPowerDetailResp, AdsPowerQuery, AdsPowerReq>, IService<AdsPowerDO> {}