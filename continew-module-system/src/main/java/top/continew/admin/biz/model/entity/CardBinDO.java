package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 卡头管理实体
 *
 * <AUTHOR>
 * @since 2025/09/02 15:44
 */
@Data
@TableName("biz_card_bin")
public class CardBinDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 卡头号码
     */
    private String cardBin;

    /**
     * 卡头名称
     */
    private String name;

    /**
     * 所属平台
     */
    private CardPlatformEnum platform;

    /**
     * 卡组织
     */
    private String cardScheme;

    /**
     * 是否启用
     */
    private Boolean enable;


    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}