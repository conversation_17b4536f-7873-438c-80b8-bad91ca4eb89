package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.TestOrderDO;
import top.continew.admin.biz.model.req.AdAccountTestOrderReq;
import top.continew.admin.biz.model.req.TestOrderStatusReq;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.TestOrderQuery;
import top.continew.admin.biz.model.req.TestOrderReq;
import top.continew.admin.biz.model.resp.TestOrderDetailResp;
import top.continew.admin.biz.model.resp.TestOrderResp;

/**
 * 测试任务业务接口
 *
 * <AUTHOR>
 * @since 2025/05/13 11:43
 */
public interface TestOrderService extends BaseService<TestOrderResp, TestOrderDetailResp, TestOrderQuery, TestOrderReq>, IService<TestOrderDO> {

    void updateStatus(Long id, TestOrderStatusReq req);

    void addOrder(AdAccountTestOrderReq req);
}