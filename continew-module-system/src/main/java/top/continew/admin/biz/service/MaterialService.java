package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.MaterialDO;
import top.continew.admin.biz.model.query.MaterialQuery;
import top.continew.admin.biz.model.query.MaterialStatQuery;
import top.continew.admin.biz.model.req.MaterialReq;
import top.continew.admin.biz.model.resp.MaterialDetailResp;
import top.continew.admin.biz.model.resp.MaterialResp;
import top.continew.admin.biz.model.resp.MaterialStatByDateResp;
import top.continew.admin.biz.model.resp.MaterialStatByTypeResp;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseService;

import java.math.BigDecimal;
import java.util.List;

/**
 * 物料业务接口
 *
 * <AUTHOR>
 * @since 2025/01/21 16:20
 */
public interface MaterialService extends BaseService<MaterialResp, MaterialDetailResp, MaterialQuery, MaterialReq>, IService<MaterialDO> {

    BigDecimal calculateUnitPrice(MaterialResp materialResp);

    PageResp<MaterialStatByDateResp> selectStatByDatePage(MaterialStatQuery query, PageQuery pageQuery);

    List<MaterialStatByDateResp> selectStatByDateList(MaterialStatQuery query);

    List<MaterialStatByTypeResp> selectStatByTypeList(MaterialStatQuery query, String[] sort);
}