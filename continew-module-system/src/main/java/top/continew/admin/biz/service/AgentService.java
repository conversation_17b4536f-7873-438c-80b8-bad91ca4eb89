package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.AgentDO;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.AgentQuery;
import top.continew.admin.biz.model.req.AgentReq;
import top.continew.admin.biz.model.resp.AgentDetailResp;
import top.continew.admin.biz.model.resp.AgentResp;

/**
 * 中介业务接口
 *
 * <AUTHOR>
 * @since 2025/07/19 11:09
 */
public interface AgentService extends BaseService<AgentResp, AgentDetailResp, AgentQuery, AgentReq> , IService<AgentDO> {}