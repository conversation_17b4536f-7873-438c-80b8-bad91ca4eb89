package top.continew.admin.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import top.continew.admin.biz.model.entity.CustomerRequirementDO;
import top.continew.admin.biz.model.req.AdAccountOrderAddReq;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.CustomerRequirementQuery;
import top.continew.admin.biz.model.req.CustomerRequirementReq;
import top.continew.admin.biz.model.resp.CustomerRequirementDetailResp;
import top.continew.admin.biz.model.resp.CustomerRequirementResp;

import java.util.List;
import java.util.Map;

/**
 * 客户需求业务接口
 *
 * <AUTHOR>
 * @since 2025/02/20 14:03
 */
public interface CustomerRequirementService extends BaseService<CustomerRequirementResp, CustomerRequirementDetailResp, CustomerRequirementQuery, CustomerRequirementReq>, IService<CustomerRequirementDO> {
    List<Map<String, Object>> getSummary(CustomerRequirementQuery query);
    
    /**
     * 批量处理客户需求
     *
     * @param ids 需求ID列表
     */
    void accept(List<Long> ids);

    void finish(Long id, Integer finishQuantity);

    void cancel(Long id, String cancelReason);

    void invited(Long id);

    void wait(Long id);

    Integer getRequiredNum(CustomerRequirementQuery query);

    Long customerApply(AdAccountOrderAddReq req);
}