package top.continew.admin.biz.service;

import top.continew.admin.biz.model.query.BusinessManagerBanStatQuery;
import top.continew.admin.biz.model.query.BusinessManagerChannelStatQuery;
import top.continew.admin.biz.model.query.BusinessManagerStatisticsQuery;
import top.continew.admin.biz.model.req.BusinessManagerStatisticsReq;
import top.continew.admin.biz.model.req.SpendStatisticsReq;
import top.continew.admin.biz.model.resp.*;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseService;

import java.time.LocalDate;
import java.util.List;

/**
 * 成本分析业务接口
 *
 * <AUTHOR>
 * @since 2025/03/17 15:02
 */
public interface BusinessManagerStatisticsService extends BaseService<BusinessManagerStatisticsResp, BusinessManagerStatisticsDetailResp, BusinessManagerStatisticsQuery, BusinessManagerStatisticsReq> {
    void stat(LocalDate date);

    /**
     * 获取消耗统计汇总数据
     *
     * @param req 统计请求
     * @return 汇总数据
     */
    SpendStatisticsSummaryResp getSpendSummary(SpendStatisticsReq req);

    /**
     * 获取每日消耗记录
     *
     * @param req 统计请求
     * @return 每日消耗记录列表
     */
    List<DailySpendResp> getDailySpend(SpendStatisticsReq req);

    /**
     * 获取成本分析汇总数据
     *
     * @param query
     * @return
     */
    BusinessManagerStatisticsSummaryResp getSummary(BusinessManagerStatisticsQuery query);

    /**
     * BM渠道分析
     * @param query
     */
    PageResp<BusinessManagerChannelStatDataResp> statChannelData(BusinessManagerChannelStatQuery query);

    /**
     * BM封禁分析
     * @param query
     */
    PageResp<BusinessManagerBanStatDataResp> statBanData(BusinessManagerBanStatQuery query);

    void syncData(Long id);
}