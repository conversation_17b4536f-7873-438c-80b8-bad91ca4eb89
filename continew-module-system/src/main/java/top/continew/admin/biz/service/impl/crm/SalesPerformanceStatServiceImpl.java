package top.continew.admin.biz.service.impl.crm;

import cn.crane4j.core.support.OperateTemplate;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.mapper.crm.SalesPerformanceStatMapper;
import top.continew.admin.biz.model.entity.SalesPersonnelConfigDO;
import top.continew.admin.biz.model.entity.SalesPersonnelMonthlyDataDO;
import top.continew.admin.biz.model.query.CustomerStatQuery;
import top.continew.admin.biz.model.query.PerformanceStatisticsQuery;
import top.continew.admin.biz.model.query.crm.SalesPerformanceStatQuery;
import top.continew.admin.biz.model.req.*;
import top.continew.admin.biz.model.resp.*;
import top.continew.admin.biz.model.resp.crm.SalesPerformanceStatResp;
import top.continew.admin.biz.service.SalesPersonnelConfigService;
import top.continew.admin.biz.service.SalesPersonnelMonthlyDataService;
import top.continew.admin.biz.service.crm.SalesPerformanceStatService;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.admin.biz.utils.WorkUtils;
import top.continew.admin.common.enums.DisEnableStatusEnum;
import top.continew.admin.system.enums.JobRankEnum;
import top.continew.admin.system.model.entity.UserDO;
import top.continew.admin.system.service.UserService;
import top.continew.starter.cache.redisson.util.RedisUtils;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.*;

import static top.continew.admin.common.constant.CacheConstants.EXCLUDED_USERS_KEY;

/**
 * 商务业绩统计 Service 实现
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SalesPerformanceStatServiceImpl implements SalesPerformanceStatService {

    private final SalesPerformanceStatMapper salesPerformanceStatMapper;

    private final UserService userService;

    private final SalesPersonnelMonthlyDataService salesPersonnelMonthlyDataService;

    private final SalesPersonnelConfigService salesPersonnelConfigService;


    @Override
    public List<SalesPerformanceStatResp> listSalesPerformanceStat(SalesPerformanceStatQuery query) {
        return salesPerformanceStatMapper.selectSalesPerformanceStat(
                query.getSalesUserIds(),
                query.getStartDate(),
                query.getEndDate(),
                query.getUserStatus()
        );
    }

    @Override
    public List<SalesPersonnelMonthlyDataResp> listBusinessPerformanceStat(PerformanceStatisticsQuery query) {
        // 参数验证
        if (query == null || query.getJobRank() == null) {
            throw new BusinessException("查询参数或职级不能为空");
        }

        // 从 Redis 获取需要过滤的用户ID列表
        List<Long> excludedUserIds = RedisUtils.getList(EXCLUDED_USERS_KEY);

        // 对应职级的用户
        List<UserDO> userList = userService.lambdaQuery()
                .eq(UserDO::getDeptId, 702164640200656226L)
                .in(CollUtil.isNotEmpty(query.getSalesIds()), UserDO::getId, query.getSalesIds())
                .notIn(CollUtil.isNotEmpty(excludedUserIds), UserDO::getId, excludedUserIds)
                .eq(null != query.getJobRank(), UserDO::getJobRank, query.getJobRank())
                .eq(query.getUserStatus() != null, UserDO::getStatus, query.getUserStatus())
                .list();

        if (userList.isEmpty()) {
            return List.of();
        }

        List<Long> ids = userList.stream().map(BaseIdDO::getId).toList();

        // 根据职级分支处理
        return switch (query.getJobRank()) {
            case PROBATION -> calculateProbationStat(ids);
            case TRIAL -> calculateTrialStat(ids, query);
            case FORMAL -> calculateFormalStat(ids, query);
        };
    }

    @Override
    public SalesDataSummaryResp getSalesDataSummary(LocalDateTime startTime, LocalDateTime endTime, DisEnableStatusEnum userStatus) {
        return salesPerformanceStatMapper.selectSalesDataSummary(startTime, endTime, userStatus);
    }

    @Override
    public void submit(SalesPerformanceSubmitReq req) {
        if (req.getJobRank() != JobRankEnum.PROBATION && !WorkUtils.isFullMonthRange(req.getStartDate(), req.getEndDate())) {
            throw new BusinessException("时间范围不是完整月份");
        }

        List<SalesPersonnelMonthlyDataDO> list = new ArrayList<>();
        List<SalesPersonnelMonthlyDataReq> reqs = req.getList();
        for (SalesPersonnelMonthlyDataReq salesPersonnelMonthlyDataReq : reqs) {
            SalesPersonnelMonthlyDataDO salesPersonnelMonthlyData = new SalesPersonnelMonthlyDataDO();
            BeanUtil.copyProperties(salesPersonnelMonthlyDataReq, salesPersonnelMonthlyData);
            salesPersonnelMonthlyData.setJobRank(req.getJobRank());
            if (req.getJobRank() != JobRankEnum.PROBATION) {
                salesPersonnelMonthlyData.setSalesDate(req.getStartDate().toLocalDate());
            }
            list.add(salesPersonnelMonthlyData);
        }
        salesPersonnelMonthlyDataService.saveOrUpdateBatch(list);
    }

    @Override
    public void submitConfig(SalesPersonnelConfigReq req) {
        if (!WorkUtils.isFullMonthRange(req.getStartDate(), req.getEndDate())) {
            throw new BusinessException("时间范围不是完整月份");
        }
        LocalDate salesDate = req.getStartDate().toLocalDate();
        SalesPersonnelConfigDO config = salesPersonnelConfigService.getOne(new LambdaQueryWrapper<SalesPersonnelConfigDO>()
                .eq(SalesPersonnelConfigDO::getBusinessUserId, req.getBusinessUserId())
                .eq(SalesPersonnelConfigDO::getSalesDate, salesDate));
        SalesPersonnelConfigDO salesPersonnelConfigDO = new SalesPersonnelConfigDO();
        salesPersonnelConfigDO.setBusinessUserId(req.getBusinessUserId());
        salesPersonnelConfigDO.setSalesDate(salesDate);
        salesPersonnelConfigDO.setData(req.getData());
        if (config != null) {
            salesPersonnelConfigDO.setId(config.getId());
            salesPersonnelConfigService.updateById(salesPersonnelConfigDO);
        } else {
            salesPersonnelConfigService.save(salesPersonnelConfigDO);
        }
    }

    @Override
    public List<SalesPersonnelMonthlyDataDO> info(Long id, YearMonth yearMonth, JobRankEnum jobRank) {

        LocalDate date = yearMonth != null ? yearMonth.atDay(1) : null;

        return salesPersonnelMonthlyDataService.lambdaQuery()
                .eq(SalesPersonnelMonthlyDataDO::getBusinessUserId, id)
                .eq(yearMonth != null && jobRank != JobRankEnum.PROBATION, SalesPersonnelMonthlyDataDO::getSalesDate, date)
                .eq(SalesPersonnelMonthlyDataDO::getJobRank, jobRank)
                .list();
    }

    @Override
    public List<CustomerPerformanceStatResp> customerStat(CustomerStatQuery query) {
        LocalDateTime startTime = query.getYearMonth().atDay(1).atStartOfDay();
        LocalDateTime endTime = query.getYearMonth().atEndOfMonth().atTime(23, 59, 59);

        // 从 Redis 获取需要过滤的用户ID列表
        List<Long> excludedUserIds = RedisUtils.getList(EXCLUDED_USERS_KEY);

        List<CustomerPerformanceStatResp> base = salesPerformanceStatMapper.selectCustomerStatBase(query.getBusinessUserIds(), startTime, endTime, query.getUserStatus(), excludedUserIds);
        LocalDateTime previousMonth = startTime.minusMonths(1);
        LocalDateTime previousMonthEnd = endTime.minusMonths(1);
        for (CustomerPerformanceStatResp customerPerformanceStatResp : base) {
            if (customerPerformanceStatResp.getFirstRechargeNewCustomerCount() > 0) {
                Map<String, Object> map = salesPerformanceStatMapper.selectCustomerFirstMonthConsumption(customerPerformanceStatResp.getBusinessUserId(), previousMonth, previousMonthEnd);
                customerPerformanceStatResp.setFirstMonthConsumptionQualifiedNewCustomerCount3KTo10K(MapUtil.getInt(map, "1Count", 0));
                customerPerformanceStatResp.setFirstMonthConsumptionQualifiedNewCustomerCount10KTo30K(MapUtil.getInt(map, "2Count", 0));
                customerPerformanceStatResp.setFirstMonthConsumptionQualifiedNewCustomerCount30KTo50K(MapUtil.getInt(map, "3Count", 0));
            }
        }

        // 对结果进行排序：按成交客户数（firstRechargeNewCustomerCount）和有效拉群数（validGroupCount）倒序
        base.sort(Comparator
                .comparing(CustomerPerformanceStatResp::getFirstRechargeNewCustomerCount, Comparator.nullsLast(Comparator.reverseOrder()))
                .thenComparing(CustomerPerformanceStatResp::getValidGroupCount, Comparator.nullsLast(Comparator.reverseOrder())));

        return base;
    }

    @Override
    public List<CustomerPerformanceOpportunityResp> customerStatOpportunity(CustomerPerformanceOpportunityReq req) {
        LocalDateTime startTime = req.getYearMonth().atDay(1).atStartOfDay();
        LocalDateTime endTime = req.getYearMonth().atEndOfMonth().atTime(23, 59, 59);
        return salesPerformanceStatMapper.selectCustomerStatOpportunity(req.getBusinessUserId(), startTime, endTime);
    }

    @Override
    public List<CustomerPerformanceInfoResp> customerStatCustomerInfo(CustomerPerformanceInfoReq req) {
        LocalDateTime startTime = req.getYearMonth().atDay(1).atStartOfDay();
        LocalDateTime endTime = req.getYearMonth().atEndOfMonth().atTime(23, 59, 59);
        if (req.getType() == 1) {
            return salesPerformanceStatMapper.selectCustomerStatFirstRechargeNewCustomer(req.getBusinessUserId(), startTime, endTime);
        } else if (req.getType() == 2 || req.getType() == 3 || req.getType() == 4) {
            LocalDateTime previousMonth = startTime.minusMonths(1);
            LocalDateTime previousMonthEnd = endTime.minusMonths(1);
            return salesPerformanceStatMapper.selectCustomerFirstMonthConsumptionQualifiedNewCustomer(req.getBusinessUserId(), previousMonth, previousMonthEnd, req.getType());
        } else {
            return List.of();
        }
    }

    @Override
    public List<CustomerRefundPerformanceStatResp> refundCustomerStat(CustomerStatQuery query) {
        LocalDateTime startTime = query.getYearMonth().atDay(1).atStartOfDay();
        LocalDateTime endTime = query.getYearMonth().atEndOfMonth().atTime(23, 59, 59);

        // 从 Redis 获取需要过滤的用户ID列表
        List<Long> excludedUserIds = RedisUtils.getList(EXCLUDED_USERS_KEY);

        List<CustomerRefundPerformanceStatResp> base = salesPerformanceStatMapper.selectRefundCustomerStatBase(query.getBusinessUserIds(), startTime, endTime, query.getUserStatus(), excludedUserIds);
        LocalDateTime previousMonth = startTime.minusMonths(1);
        LocalDateTime previousMonthEnd = endTime.minusMonths(1);
        for (CustomerRefundPerformanceStatResp customerRefundPerformanceStatResp : base) {
            customerRefundPerformanceStatResp.setBusinessUserName(userService.getById(customerRefundPerformanceStatResp.getBusinessUserId()).getNickname());
            Map<String, Object> map = salesPerformanceStatMapper.selectRefundCustomerFirstMonthConsumption(customerRefundPerformanceStatResp.getBusinessUserId(), previousMonth, previousMonthEnd);
            customerRefundPerformanceStatResp.setFirstMonthConsumptionQualifiedNewCustomerCount3KTo10K(MapUtil.getInt(map, "1Count", 0));
            customerRefundPerformanceStatResp.setFirstMonthConsumptionQualifiedNewCustomerCount3KTo10K(MapUtil.getInt(map, "2Count", 0));
            customerRefundPerformanceStatResp.setFirstMonthConsumptionQualifiedNewCustomerCount3KTo10K(MapUtil.getInt(map, "3Count", 0));
        }
        // 对结果进行排序：按激活客户数倒序
        base.sort(Comparator.comparing(CustomerRefundPerformanceStatResp::getActivatedCustomer, Comparator.nullsLast(Comparator.reverseOrder())));
        return base;
    }

    @Override
    public List<RefundCustomerStatInfoResp> refundCustomerStatInfo(CustomerPerformanceInfoReq req) {
        LocalDateTime startTime = req.getYearMonth().atDay(1).atStartOfDay();
        LocalDateTime endTime = req.getYearMonth().atEndOfMonth().atTime(23, 59, 59);
        if (req.getType() == 1) {
            return salesPerformanceStatMapper.selectRefundCustomerStatInfo(req.getBusinessUserId(), startTime, endTime);
        } else if (req.getType() == 2 || req.getType() == 3 || req.getType() == 4) {
            LocalDateTime previousMonth = startTime.minusMonths(1);
            LocalDateTime previousMonthEnd = endTime.minusMonths(1);
            return salesPerformanceStatMapper.selectRefundCustomerFirstMonth(req.getBusinessUserId(), previousMonth, previousMonthEnd, req.getType());
        } else {
            return List.of();
        }
    }

    /**
     * 计算试岗期业绩统计
     * 试岗期关注：微信好友数量、Telegram好友数量、客咨漏填写数量
     */
    private List<SalesPersonnelMonthlyDataResp> calculateProbationStat(List<Long> ids) {
        List<SalesPersonnelMonthlyDataDO> existingData = salesPersonnelMonthlyDataService.listByUserIds(ids, JobRankEnum.PROBATION, null);

        List<SalesPersonnelMonthlyDataResp> result = new ArrayList<>();

        // 如果没有现有数据，直接从统计表获取所有数据
        if (CollUtil.isEmpty(existingData)) {
            List<BusinessPerformanceStatisticsResp> stats = salesPerformanceStatMapper.selectProbationStat(ids);
            result.addAll(convertToResponseList(stats));
        } else {
            // 转换现有数据
            result.addAll(convertExistingData(existingData));

            // 获取缺失用户的统计数据
            List<Long> existingUserIds = existingData.stream()
                    .map(SalesPersonnelMonthlyDataDO::getBusinessUserId)
                    .toList();
            List<Long> missingUserIds = (List<Long>) CollUtil.subtract(ids, existingUserIds);

            if (CollUtil.isNotEmpty(missingUserIds)) {
                List<BusinessPerformanceStatisticsResp> missingStats = salesPerformanceStatMapper.selectProbationStat(missingUserIds);
                result.addAll(convertToResponseList(missingStats));
            }
        }

        finalizeResult(result);
        return result;
    }


    /**
     * 计算试用期业绩统计
     * 试用期关注：成交客户数、意向客户数、交友数、客户总消耗、日报漏填写数量、客咨漏填写数量
     */
    private List<SalesPersonnelMonthlyDataResp> calculateTrialStat(List<Long> ids, PerformanceStatisticsQuery query) {
        return calculateStatWithTimeRange(ids, JobRankEnum.TRIAL, query, salesPerformanceStatMapper::selectTrialStat);
    }

    /**
     * 计算正式期业绩统计
     * 正式期关注：新成交客户数、广告户使用率、客户单户平均消耗、客户留存率、交友数、客户总消耗、日报漏填写数量、客咨漏填写数量
     */
    private List<SalesPersonnelMonthlyDataResp> calculateFormalStat(List<Long> ids, PerformanceStatisticsQuery query) {
        return calculateStatWithTimeRange(ids, JobRankEnum.FORMAL, query, salesPerformanceStatMapper::selectFormalStat);
    }


    /**
     * 通用的带时间范围的统计计算方法
     */
    private List<SalesPersonnelMonthlyDataResp> calculateStatWithTimeRange(
            List<Long> ids,
            JobRankEnum jobRank,
            PerformanceStatisticsQuery query,
            StatQueryFunction queryFunction) {

        CheckUtils.throwIf(query.getStartDate() == null || query.getEndDate() == null, "开始时间和结束时间不能为空");

        List<SalesPersonnelMonthlyDataResp> result = new ArrayList<>();

        if (WorkUtils.isFullMonthRange(query.getStartDate(), query.getEndDate())) {
            // 完整月份：优先使用月度数据
            YearMonth month = YearMonth.from(query.getStartDate());
            List<SalesPersonnelMonthlyDataDO> existingData = salesPersonnelMonthlyDataService.listByUserIds(ids, jobRank, month);

            if (CollUtil.isEmpty(existingData)) {
                // 没有月度数据，使用统计数据
                int workingDays = WorkUtils.getWorkingDaysInRange(query.getStartDate().toLocalDate(), query.getEndDate().toLocalDate());
                List<BusinessPerformanceStatisticsResp> stats = queryFunction.query(ids, query.getStartDate(), query.getEndDate(), workingDays);
                List<SalesPersonnelMonthlyDataResp> salesPersonnelMonthlyDataRests = convertToResponseList(stats);
                if (jobRank.equals(JobRankEnum.FORMAL)) {
                    salesPersonnelMonthlyDataRests.forEach(item -> item.setConfig(salesPersonnelConfigService.getNewConfig(item.getBusinessUserId())));
                }
                result.addAll(salesPersonnelMonthlyDataRests);
            } else {
                List<SalesPersonnelMonthlyDataResp> resp = convertExistingData(existingData);
                // 正式期获取配置
                if (jobRank.equals(JobRankEnum.FORMAL)) {
                    resp.forEach(item -> item.setConfig(salesPersonnelConfigService.getNewConfig(item.getBusinessUserId())));
                }

                // 转换现有月度数据
                result.addAll(resp);

                // 补充缺失用户的统计数据
                List<Long> existingUserIds = existingData.stream()
                        .map(SalesPersonnelMonthlyDataDO::getBusinessUserId)
                        .toList();
                List<Long> missingUserIds = (List<Long>) CollUtil.subtract(ids, existingUserIds);

                if (CollUtil.isNotEmpty(missingUserIds)) {
                    int workingDays = WorkUtils.getWorkingDaysInRange(query.getStartDate().toLocalDate(), query.getEndDate().toLocalDate());
                    List<BusinessPerformanceStatisticsResp> missingStats = queryFunction.query(missingUserIds, query.getStartDate(), query.getEndDate(), workingDays);
                    result.addAll(convertToResponseList(missingStats));
                }
            }
        } else {
            // 非完整月份：直接使用统计数据
            int workingDays = WorkUtils.getWorkingDaysInRange(query.getStartDate().toLocalDate(), query.getEndDate().toLocalDate());
            List<BusinessPerformanceStatisticsResp> stats = queryFunction.query(ids, query.getStartDate(), query.getEndDate(), workingDays);
            result.addAll(convertToResponseList(stats));
        }

        finalizeResult(result);
        return result;
    }

    /**
     * 统计查询函数式接口
     */
    @FunctionalInterface
    private interface StatQueryFunction {
        List<BusinessPerformanceStatisticsResp> query(List<Long> ids, LocalDateTime start, LocalDateTime end, int workingDays);
    }

    /**
     * 批量转换 BusinessPerformanceStatisticsResp 到 SalesPersonnelMonthlyDataResp
     */
    private List<SalesPersonnelMonthlyDataResp> convertToResponseList(List<BusinessPerformanceStatisticsResp> stats) {
        List<SalesPersonnelMonthlyDataResp> result = new ArrayList<>();
        for (BusinessPerformanceStatisticsResp stat : stats) {
            result.add(convertToResponse(stat));
        }
        return result;
    }

    /**
     * 转换 BusinessPerformanceStatisticsResp 到 SalesPersonnelMonthlyDataResp
     */
    private SalesPersonnelMonthlyDataResp convertToResponse(BusinessPerformanceStatisticsResp source) {
        SalesPersonnelMonthlyDataResp target = new SalesPersonnelMonthlyDataResp();
        // 忽略 null 值，保留目标对象的默认值
        BeanUtil.copyProperties(source, target, CopyOptions.create().ignoreNullValue());
        return target;
    }

    /**
     * 转换现有月度数据到响应对象
     */
    private List<SalesPersonnelMonthlyDataResp> convertExistingData(List<SalesPersonnelMonthlyDataDO> existingData) {
        List<SalesPersonnelMonthlyDataResp> result = new ArrayList<>();
        for (SalesPersonnelMonthlyDataDO data : existingData) {
            SalesPersonnelMonthlyDataResp resp = new SalesPersonnelMonthlyDataResp();
            // 忽略 null 值，保留目标对象的默认值
            BeanUtil.copyProperties(data, resp, CopyOptions.create().ignoreNullValue());
            result.add(resp);
        }
        return result;
    }

    /**
     * 执行最终的数据处理：数据填充和排序
     */
    private void finalizeResult(List<SalesPersonnelMonthlyDataResp> result) {
        // 执行数据填充
        OperateTemplate operateTemplate = SpringUtil.getBean(OperateTemplate.class);
        operateTemplate.execute(result);

        // 按 businessName 排序
        result.sort(Comparator.comparing(SalesPersonnelMonthlyDataResp::getBusinessName,
                Comparator.nullsLast(Comparator.naturalOrder())));
    }
}