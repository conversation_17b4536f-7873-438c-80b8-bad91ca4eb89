package top.continew.admin.biz.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import top.continew.admin.biz.config.TelegramChatIdConfig;
import top.continew.admin.biz.enums.AdAccountOrderStatusEnum;
import top.continew.admin.biz.event.TelegramMessageEvent;
import top.continew.admin.biz.mapper.FbAdCampaignsMapper;
import top.continew.admin.biz.mapper.FbAdMapper;
import top.continew.admin.biz.mapper.FbAdSetsMapper;
import top.continew.admin.biz.model.entity.AdAccountOrderDO;
import top.continew.admin.biz.model.entity.FbAdCampaignsDO;
import top.continew.admin.biz.model.entity.FbAdDO;
import top.continew.admin.biz.model.entity.FbAdSetsDO;
import top.continew.admin.biz.utils.RabbitUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
@RequiredArgsConstructor
@Slf4j
public class AdAccountSyncService {

    private final AdAccountOrderService adAccountOrderService;
    private final TelegramChatIdConfig telegramChatIdConfig;
    private final FbAdCampaignsMapper fbAdCampaignsMapper;
    private final FbAdSetsMapper fbAdSetsMapper;
    private final FbAdMapper fbAdMapper;

    /**
     * 批量处理大小，避免单次操作数据量过大
     */
    private static final int BATCH_SIZE = 200;


    /**
     * 最大重试次数
     */
    private static final int MAX_DEADLOCK_RETRIES = 3;

    /**
     * 基础重试延迟（毫秒）
     */
    private static final long BASE_RETRY_DELAY = 100;
    

    public void getAllCampaigns(String envId, String serialNumber, String proxy, String headers, boolean isLite) {
        String response;
        if (StringUtils.isNotBlank(serialNumber)) {
            response = RabbitUtils.getAllCampaignsV2(serialNumber, isLite, proxy, headers);
        } else {
            response = RabbitUtils.getAllCampaigns(envId, isLite);
        }
        String browserKey = StringUtils.defaultIfBlank(envId, serialNumber);
        JSONObject jsonObject = JSONObject.parseObject(response);
        int code = jsonObject.getInteger("code");
        if (code != 0) {
            log.error("【{}】广告系列同步失败：{}", browserKey, jsonObject.getString("msg"));
            return;
        }
        JSONArray data = jsonObject.getJSONArray("data");
        for (int i = 0; i < data.size(); i++) {
            JSONObject dataItem = data.getJSONObject(i);
            int itemCode = dataItem.getIntValue("code");
            JSONObject item = dataItem.getJSONObject("body");
            String platformAdId = item.getString("account_id");
            if (itemCode != 200) {
                log.error("【{}】{}广告系列同步失败：{}", browserKey, platformAdId, item);
                continue;
            }
            int accountStatus = item.getIntValue("account_status");
            if (accountStatus != 1) {
                continue;
            }
            JSONObject campaigns = item.getJSONObject("campaigns");
            if (campaigns == null) {
                continue;
            }
            JSONArray campaignsData = campaigns.getJSONArray("data");
            if (campaignsData == null || campaignsData.isEmpty()) {
                continue;
            }
            AdAccountOrderDO order = adAccountOrderService.getOne(Wrappers.<AdAccountOrderDO>lambdaQuery()
                .eq(AdAccountOrderDO::getAdAccountId, platformAdId)
                .eq(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED));
            if (order == null) {
                continue;
            }
            if (order.getStartCampaignTime() != null) {
                continue;
            }
            LocalDateTime startCampaignTime = null;
            for (int i1 = campaignsData.size() - 1; i1 >= 0; i1--) {
                JSONObject campaignItem = campaignsData.getJSONObject(i1);
                String startTimeStr = campaignItem.getString("start_time");
                if (StringUtils.isBlank(startTimeStr)) {
                    continue;
                }
                if (!"ACTIVE".equals(campaignItem.getString("status"))) {
                    continue;
                }
                OffsetDateTime offsetDateTime = OffsetDateTime.parse(startTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssZ"));
                // 转换为北京时区（UTC+8）
                ZonedDateTime beijingTime = offsetDateTime.atZoneSameInstant(ZoneId.of("Asia/Shanghai"));
                LocalDateTime startTime = beijingTime.toLocalDateTime();
                if (startTime.isAfter(order.getFinishTime())) {
                    startCampaignTime = startTime;
                    break;
                } else {
                    SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                        .chatId(telegramChatIdConfig.getMonitorChatId())
                        .text("广告户 %s 存在未关闭的养户广告❌❌❌，请检查".formatted(platformAdId))
                        .build()));
                }
            }
            if (startCampaignTime != null) {
                adAccountOrderService.update(Wrappers.<AdAccountOrderDO>lambdaUpdate()
                    .set(AdAccountOrderDO::getStartCampaignTime, startCampaignTime)
                    .eq(AdAccountOrderDO::getId, order.getId()));
            }
        }
    }

    public void getAllAdSets(String envId, String serialNumber, String proxy, String headers, boolean isLite) {
        String response = RabbitUtils.getAllAdSets(serialNumber, isLite, proxy, headers);
        String browserKey = StringUtils.defaultIfBlank(envId, serialNumber);
        JSONObject jsonObject = JSONObject.parseObject(response);
        int code = jsonObject.getInteger("code");
        if (code != 0) {
            log.error("【{}】广告组同步失败：{}", browserKey, jsonObject.getString("msg"));
            return;
        }
        JSONArray data = jsonObject.getJSONArray("data");
        for (int i = 0; i < data.size(); i++) {
            JSONObject dataItem = data.getJSONObject(i);
            int itemCode = dataItem.getIntValue("code");
            JSONObject item = dataItem.getJSONObject("body");
            String platformAdId = item.getString("account_id");
            if (itemCode != 200) {
                log.error("【{}】{}广告组同步失败：{}", browserKey, platformAdId, item);
                continue;
            }
            JSONArray jsonArray = item.getJSONArray("data");
            if (jsonArray == null || jsonArray.isEmpty()) {
                continue;
            }
            AdAccountOrderDO order = adAccountOrderService.getOne(Wrappers.<AdAccountOrderDO>lambdaQuery()
                .eq(AdAccountOrderDO::getAdAccountId, platformAdId)
                .eq(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED));
            if (order == null) {
                continue;
            }
            int oneDollarCount = 0;
            for (int i1 = 0; i1 < jsonArray.size(); i1++) {
                JSONObject adSetItem = jsonArray.getJSONObject(i1);
                BigDecimal dailyBudget = adSetItem.getBigDecimal("daily_budget");
                if (dailyBudget != null && dailyBudget.compareTo(new BigDecimal(200)) < 0) {
                    oneDollarCount++;
                }
            }
            boolean isOneDollar = oneDollarCount >= 10;
            adAccountOrderService.update(Wrappers.<AdAccountOrderDO>lambdaUpdate()
                .set(AdAccountOrderDO::getIsOneDollar, isOneDollar)
                .eq(AdAccountOrderDO::getId, order.getId()));
        }
    }


    /**
     * 获取并同步所有广告信息
     * 高效处理大量Facebook广告数据，支持批量插入/更新
     *
     * @param envId        环境ID
     * @param serialNumber 序列号
     * @param proxy        代理
     * @param headers      请求头
     * @param isLite       是否轻量级
     */
    public void getAllAdInfos(String envId, String serialNumber, String proxy, String headers, boolean isLite) {
        String response = RabbitUtils.getAllAdInfos(serialNumber, isLite, proxy, headers);
        String browserKey = StringUtils.defaultIfBlank(envId, serialNumber);
        
        try {
            JSONObject jsonObject = JSONObject.parseObject(response);
            //先判断code是不是整数，如果不是则也是失败，其可能是ECONNREFUSED
            Integer code;
            try {
                code = jsonObject.getInteger("code");
            } catch (Exception e) {
                log.error("全部广告信息-【{}】解析响应code失败，可能是网络错误: {}", browserKey, response);
                return;
            }

            if (code == null || code != 0) {
                log.error("全部广告信息-【{}】同步失败：{}", browserKey,
                        jsonObject.getString("msg") != null ? jsonObject.getString("msg") : "未知错误");
                return;
            }
            
            JSONArray data = jsonObject.getJSONArray("data");
            if (data == null || data.isEmpty()) {
                log.info("全部广告信息-【{}】没有广告数据需要同步", browserKey);
                return;
            }
            
            // 统计处理结果
            int totalAccounts = data.size();
            int successCount = 0;
            int errorCount = 0;
            
            log.info("全部广告信息-【{}】开始处理 {} 个广告账户的数据", browserKey, totalAccounts);
            
            for (int i = 0; i < data.size(); i++) {
                JSONObject dataItem = data.getJSONObject(i);
                try {
                    boolean success = processAccountData(dataItem, browserKey);
                    if (success) {
                        successCount++;
                    } else {
                        errorCount++;
                    }
                } catch (Exception e) {
                    errorCount++;
                    log.error("全部广告信息-【{}】处理第 {} 个账户数据时发生异常", browserKey, i + 1, e);
                }
            }
            
            log.info("全部广告信息-【{}】广告信息同步完成，总计：{}，成功：{}，失败：{}",
                    browserKey, totalAccounts, successCount, errorCount);
                    
        } catch (Exception e) {
            log.error("全部广告信息-【{}】解析广告信息响应时发生异常", browserKey, e);
            throw new RuntimeException("广告信息同步失败", e);
        }
    }

    /**
     * 处理单个账户的广告数据
     *
     * @param dataItem   账户数据
     * @param browserKey 浏览器标识
     * @return 是否处理成功
     */
    private boolean processAccountData(JSONObject dataItem, String browserKey) {
        int itemCode = dataItem.getIntValue("code");
        JSONObject item = dataItem.getJSONObject("body");
        String platformAdId = item.getString("account_id");

        if (item == null) {
            log.warn("全部广告信息-【{}】账户数据为空", browserKey);
            //需要把所有关联的广告系列、广告组、广告都删除
            LocalDateTime syncTime = LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS);
            cleanupObsoleteDataByUpdateTime(platformAdId, syncTime);
            return false;
        }
        

        
        if (itemCode != 200) {
            log.error("全部广告信息-【{}】{}同步失败：{}", browserKey, platformAdId, item);
            return false;
        }
        

        // 获取广告系列数据
        JSONObject campaigns = item.getJSONObject("campaigns");
        if (campaigns == null) {
            //需要把广告账号下的所有关联的广告系列、广告组、广告都删除
            LocalDateTime syncTime = LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS);
            cleanupObsoleteDataByUpdateTime(platformAdId, syncTime);
            log.warn("全部广告信息-【{}】{}没有广告系列数据", browserKey, platformAdId);
            return true;
        }
        
        JSONArray campaignsData = campaigns.getJSONArray("data");
        if (campaignsData == null || campaignsData.isEmpty()) {
            //需要把广告账号下的所有关联的广告系列、广告组、广告都删除
            LocalDateTime syncTime = LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS);
            cleanupObsoleteDataByUpdateTime(platformAdId, syncTime);
            log.warn("全部广告信息-【{}】{}广告系列数据为空", browserKey, platformAdId);
            return true;
        }
        
        try {
            // 解析并批量保存广告数据
            parseAndSaveAdData(platformAdId, campaignsData);
            
            return true;
            
        } catch (Exception e) {
            log.error("全部广告信息-【{}】{}处理广告数据时发生异常", browserKey, platformAdId, e);
            return false;
        }
    }

    /**
     * 解析并批量保存广告数据（包含数据清理）
     *
     * @param adAccountId   广告账户ID
     * @param campaignsData 广告系列数据
     */
    private void parseAndSaveAdData(String adAccountId, JSONArray campaignsData) {
        // 生成本次同步的固定时间戳
        LocalDateTime syncTime = LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS);


        List<FbAdCampaignsDO> campaigns = new ArrayList<>();
        List<FbAdSetsDO> adSets = new ArrayList<>();
        List<FbAdDO> ads = new ArrayList<>();
        
        for (int i = 0; i < campaignsData.size(); i++) {
            JSONObject campaignJson = campaignsData.getJSONObject(i);
            
            // 解析广告系列
            FbAdCampaignsDO campaign = parseCampaign(campaignJson, adAccountId);
            // 设置固定的更新时间
            campaign.setUpdateTime(syncTime);
            campaigns.add(campaign);
            
            // 解析广告组和广告的代码类似，同时设置相同的更新时间
            JSONObject adSetsJson = campaignJson.getJSONObject("adsets");
            if(null == adSetsJson) {
                //需要把广告系列下的广告组以及下面的广告都删除
                cleanupDataByCampaignId(adAccountId, campaign.getPlatformId());
                log.warn("全部广告信息-{}-{} 没有广告组数据", adAccountId, campaign.getPlatformId());
                continue;
            }

            JSONArray adSetsData = adSetsJson.getJSONArray("data");
            if(null == adSetsData || adSetsData.isEmpty()) {
                //需要把广告系列下的广告组以及下面的广告都删除
                cleanupDataByCampaignId(adAccountId, campaign.getPlatformId());
                log.warn("全部广告信息-{}-{} 广告组为空", adAccountId, campaign.getPlatformId());
                continue;
            }
            
            for (int j = 0; j < adSetsData.size(); j++) {
                JSONObject adSetJson = adSetsData.getJSONObject(j);

                FbAdSetsDO adSet = parseAdSet(adSetJson, adAccountId, campaign.getPlatformId());
                // 设置固定的更新时间
                adSet.setUpdateTime(syncTime);
                adSets.add(adSet);

                // 解析广告
                JSONObject adsJson = adSetJson.getJSONObject("ads");
                if(null == adsJson) {
                    //需要把广告组下面的广告都删除掉
                    cleanupDataByAdsetId(adAccountId, campaign.getPlatformId(), adSet.getPlatformId());
                    log.warn("全部广告信息{}-{}-{} 没有广告数据", adAccountId, campaign.getPlatformId(), adSet.getPlatformId());
                    continue;
                }

                JSONArray adsData = adsJson.getJSONArray("data");
                if(null == adsData || adsData.isEmpty()) {
                    //需要把广告组下面的广告都删除掉
                    cleanupDataByAdsetId(adAccountId, campaign.getPlatformId(), adSet.getPlatformId());
                    log.warn("全部广告信息-{}-{}-{} 广告为空", adAccountId, campaign.getPlatformId(), adSet.getPlatformId());
                    continue;
                }

                for (int k = 0; k < adsData.size(); k++) {
                    JSONObject adJson = adsData.getJSONObject(k);

                    FbAdDO ad = parseAd(adJson, adAccountId,
                            adSet.getPlatformId(), campaign.getPlatformId());
                    // 设置固定的更新时间
                    ad.setUpdateTime(syncTime);
                    ads.add(ad);
                }
            }
        }
        
        // 批量保存数据
        batchSaveData(campaigns, adSets, ads);
        
        // 清理更新时间不匹配的过期数据
        cleanupObsoleteDataByUpdateTime(adAccountId, syncTime);
    
        log.info("全部广告信息-账户 {} 数据同步完成：系列 {}，广告组 {}，广告 {}",
                adAccountId, campaigns.size(), adSets.size(), ads.size());
    }

    /**
     * 根据更新时间清理过期数据
     * 删除更新时间不等于本次同步时间的数据
     *
     * @param adAccountId 广告账户ID
     * @param syncTime    本次同步时间
     */
    private void cleanupObsoleteDataByUpdateTime(String adAccountId, LocalDateTime syncTime) {
        try {
            // 1. 清理广告
            executeWithDeadlockRetry(() -> {
                int deletedAds = fbAdMapper.deleteByAdAccountIdAndUpdateTimeNot(
                    adAccountId, syncTime);
                if (deletedAds > 0) {
                    log.info("全部广告信息-账户 {} 清理过期广告：{} 条", adAccountId, deletedAds);
                }
                return deletedAds;
            }, "清理过期广告", 0, 0);
            
            // 2. 清理广告组
            executeWithDeadlockRetry(() -> {
                int deletedAdSets = fbAdSetsMapper.deleteByAdAccountIdAndUpdateTimeNot(
                    adAccountId, syncTime);
                if (deletedAdSets > 0) {
                    log.info("全部广告信息-账户 {} 清理过期广告组：{} 条", adAccountId, deletedAdSets);
                }
                return deletedAdSets;
            }, "清理过期广告组", 0, 0);
            
            // 3. 清理广告系列
            executeWithDeadlockRetry(() -> {
                int deletedCampaigns = fbAdCampaignsMapper.deleteByAdAccountIdAndUpdateTimeNot(
                    adAccountId, syncTime);
                if (deletedCampaigns > 0) {
                    log.info("全部广告信息-账户 {} 清理过期广告系列：{} 条", adAccountId, deletedCampaigns);
                }
                return deletedCampaigns;
            }, "清理过期广告系列", 0, 0);
            
        } catch (Exception e) {
            log.error("全部广告信息-账户 {} 清理过期数据失败", adAccountId, e);
            // 清理失败不影响主流程，只记录错误日志
        }
    }


    /**
     * 根据广告系列ID清理广告组和广告数据
     *
     * @param campaignId 广告系列ID
     */
    private void cleanupDataByCampaignId(String adAccountId, String campaignId) {
        try {
            // 1. 删除广告系列下的所有广告
            executeWithDeadlockRetry(() -> {
                int deletedAds = fbAdMapper.deleteByCampaignId(adAccountId, campaignId);
                if (deletedAds > 0) {
                    log.info("全部广告信息-广告系列 {} 清理广告：{} 条", campaignId, deletedAds);
                }
                return deletedAds;
            }, "清理广告系列下的广告", 0, 0);

            // 2. 删除广告系列下的所有广告组
            executeWithDeadlockRetry(() -> {
                int deletedAdSets = fbAdSetsMapper.deleteByCampaignId(adAccountId, campaignId);
                if (deletedAdSets > 0) {
                    log.info("全部广告信息-广告系列 {} 清理广告组：{} 条", campaignId, deletedAdSets);
                }
                return deletedAdSets;
            }, "清理广告系列下的广告组", 0, 0);

        } catch (Exception e) {
            log.error("全部广告信息-广告系列 {} 清理数据失败", campaignId, e);
        }
    }

    /**
     * 根据广告组ID清理广告数据
     *
     * @param adsetId 广告组ID
     */
    private void cleanupDataByAdsetId(String adAccountId, String campaignId, String adsetId) {
        try {
            // 删除广告组下的所有广告
            executeWithDeadlockRetry(() -> {
                int deletedAds = fbAdMapper.deleteByAdsetId(adAccountId, campaignId, adsetId);
                if (deletedAds > 0) {
                    log.info("全部广告信息-广告组 {} 清理广告：{} 条", adsetId, deletedAds);
                }
                return deletedAds;
            }, "清理广告组下的广告", 0, 0);

        } catch (Exception e) {
            log.error("全部广告信息-广告组 {} 清理数据失败", adsetId, e);
        }
    }

    /**
     * 解析广告系列数据
     *
     * @param campaignJson 广告系列JSON
     * @param adAccountId  广告账户ID
     * @return 广告系列实体
     */
    private FbAdCampaignsDO parseCampaign(JSONObject campaignJson, String adAccountId) {
        FbAdCampaignsDO campaign = new FbAdCampaignsDO();
        campaign.setPlatformId(campaignJson.getString("id"));
        campaign.setName(campaignJson.getString("name"));
        campaign.setAdAccountId(adAccountId);
        campaign.setStatus(campaignJson.getString("status"));
        campaign.setEffectiveStatus(campaignJson.getString("effective_status"));
        JSONObject deliveryInfo = campaignJson.getJSONObject("delivery_info");
        if (deliveryInfo != null) {
            campaign.setDeliveryStatus(deliveryInfo.getString("status"));
        }
        campaign.setBidStrategy(campaignJson.getString("bid_strategy"));
        String startTimeStr = campaignJson.getString("start_time");
        if(StrUtil.isNotBlank(startTimeStr)){
            OffsetDateTime offsetDateTime = OffsetDateTime.parse(startTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssZ"));
            // 转换为北京时区（UTC+8）
            ZonedDateTime beijingTime = offsetDateTime.atZoneSameInstant(ZoneId.of("Asia/Shanghai"));
            LocalDateTime startTime = beijingTime.toLocalDateTime();
            campaign.setStartTime(startTime);
        }


        // 处理预算（Facebook返回的是字符串，单位是分）
        String dailyBudgetStr = campaignJson.getString("daily_budget");
        if (StringUtils.isNotBlank(dailyBudgetStr)) {
            try {
                campaign.setDailyBudget(Integer.valueOf(dailyBudgetStr));
            } catch (NumberFormatException e) {
                log.warn("全部广告信息-解析广告系列 {} 的每日预算失败：{}", campaign.getPlatformId(), dailyBudgetStr);
            }
        }

        String lifetimeBudgetStr = campaignJson.getString("lifetime_budget");
        if (StringUtils.isNotBlank(lifetimeBudgetStr)) {
            try {
                campaign.setLifetimeBudget(Integer.valueOf(lifetimeBudgetStr));
            } catch (NumberFormatException e) {
                log.warn("全部广告信息-解析广告系列 {} 的总预算失败：{}", campaign.getPlatformId(), lifetimeBudgetStr);
            }
        }

        return campaign;
    }

    /**
     * 解析广告组数据
     *
     * @param adSetJson    广告组JSON
     * @param adAccountId  广告账户ID
     * @param campaignId   广告系列ID
     * @return 广告组实体
     */
    private FbAdSetsDO parseAdSet(JSONObject adSetJson, String adAccountId, String campaignId) {
        FbAdSetsDO adSet = new FbAdSetsDO();
        adSet.setAdAccountId(adAccountId);
        adSet.setCampaignId(campaignId);
        adSet.setPlatformId(adSetJson.getString("id"));
        adSet.setName(adSetJson.getString("name"));
        adSet.setStatus(adSetJson.getString("status"));
        adSet.setOptimizationGoal(adSetJson.getString("optimization_goal"));
        JSONObject customEvent = adSetJson.getJSONObject("promoted_object");
        //log.info("自定义事件类型： {} {} {}", adSet.getPlatformId(), adSetJson.getString("optimization_goal"), customEvent.toJSONString());
        if (customEvent != null) {
            String customEventType = customEvent.getString("custom_event_type");
            if("OTHER".equals(customEventType)){
                adSet.setCustomEventType(customEvent.getString("custom_event_str"));

            }else {
                adSet.setCustomEventType(customEventType);
            }
        }

        adSet.setBidStrategy(adSetJson.getString("bid_strategy"));
        JSONObject deliveryInfo = adSetJson.getJSONObject("delivery_info");
        if (deliveryInfo != null) {
            adSet.setDeliveryStatus(deliveryInfo.getString("status"));
        }
        // 处理预算
        String dailyBudgetStr = adSetJson.getString("daily_budget");
        if (StringUtils.isNotBlank(dailyBudgetStr)) {
            try {
                adSet.setDailyBudget(Integer.valueOf(dailyBudgetStr));
            } catch (NumberFormatException e) {
                log.warn("全部广告信息-解析广告组 {} 的每日预算失败：{}", adSet.getPlatformId(), dailyBudgetStr);
            }
        }

        String lifetimeBudgetStr = adSetJson.getString("lifetime_budget");
        if (StringUtils.isNotBlank(lifetimeBudgetStr)) {
            try {
                adSet.setLifetimeBudget(Integer.valueOf(lifetimeBudgetStr));
            } catch (NumberFormatException e) {
                log.warn("全部广告信息-解析广告组 {} 的总预算失败：{}", adSet.getPlatformId(), lifetimeBudgetStr);
            }
        }

        return adSet;
    }

    /**
     * 解析广告数据
     *
     * @param adJson       广告JSON
     * @param adAccountId  广告账户ID
     * @param adSetId      广告组ID
     * @param campaignId   广告系列ID
     * @return 广告实体
     */
    private FbAdDO parseAd(JSONObject adJson, String adAccountId, String adSetId, String campaignId) {
        FbAdDO ad = new FbAdDO();
        ad.setPlatformId(adJson.getString("id"));
        ad.setName(adJson.getString("name"));
        ad.setAdAccountId(adAccountId);
        ad.setAdsetId(adSetId);
        ad.setCampaignId(campaignId);
        ad.setStatus(adJson.getString("status"));
        ad.setEffectiveStatus(adJson.getString("effective_status"));
        JSONObject deliveryInfo = adJson.getJSONObject("delivery_info");
        if (deliveryInfo != null) {
            ad.setDeliveryStatus(deliveryInfo.getString("status"));
        }
        // 处理预算
        String dailyBudgetStr = adJson.getString("daily_budget");
        if (StringUtils.isNotBlank(dailyBudgetStr)) {
            try {
                ad.setDailyBudget(Integer.valueOf(dailyBudgetStr));
            } catch (NumberFormatException e) {
                log.warn("全部广告信息-解析广告 {} 的每日预算失败：{}", ad.getPlatformId(), dailyBudgetStr);
            }
        }

        String lifetimeBudgetStr = adJson.getString("lifetime_budget");
        if (StringUtils.isNotBlank(lifetimeBudgetStr)) {
            try {
                ad.setLifetimeBudget(Integer.valueOf(lifetimeBudgetStr));
            } catch (NumberFormatException e) {
                log.warn("全部广告信息-解析广告 {} 的总预算失败：{}", ad.getPlatformId(), lifetimeBudgetStr);
            }
        }

        return ad;
    }

    /**
     * 批量保存数据到数据库
     *
     * @param campaigns 广告系列列表
     * @param adSets    广告组列表
     * @param ads       广告列表
     */
    private void batchSaveData(List<FbAdCampaignsDO> campaigns, List<FbAdSetsDO> adSets, List<FbAdDO> ads) {
        try {
            // 批量保存广告系列
            if (!campaigns.isEmpty()) {
                batchSaveCampaigns(campaigns);
            }
            
            // 批量保存广告组
            if (!adSets.isEmpty()) {
                batchSaveAdSets(adSets);
            }
            
            // 批量保存广告
            if (!ads.isEmpty()) {
                batchSaveAds(ads);
            }
            
        } catch (Exception e) {
            log.error("全部广告信息-批量保存数据失败", e);
            throw new RuntimeException("批量保存数据失败", e);
        }
    }

    /**
     * 批量保存广告系列（带死锁重试机制）
     *
     * @param campaigns 广告系列列表
     */
    private void batchSaveCampaigns(List<FbAdCampaignsDO> campaigns) {
        int total = campaigns.size();
        int processed = 0;
        
        while (processed < total) {
            int endIndex = Math.min(processed + BATCH_SIZE, total);
            List<FbAdCampaignsDO> batch = campaigns.subList(processed, endIndex);
            
            // 执行带重试的批量保存
            executeWithDeadlockRetry(() -> {
                int affected = fbAdCampaignsMapper.batchInsertOrUpdate(batch);
                log.debug("全部广告信息-批量保存广告系列：{}/{}, 影响行数：{}", endIndex, total, affected);
                return affected;
            }, "批量保存广告系列", processed, endIndex);
            
            processed = endIndex;
        }
    }

    /**
     * 批量保存广告组（带死锁重试机制）
     *
     * @param adSets 广告组列表
     */
    private void batchSaveAdSets(List<FbAdSetsDO> adSets) {
        int total = adSets.size();
        int processed = 0;
        
        while (processed < total) {
            int endIndex = Math.min(processed + BATCH_SIZE, total);
            List<FbAdSetsDO> batch = adSets.subList(processed, endIndex);
            
            // 执行带重试的批量保存
            executeWithDeadlockRetry(() -> {
                int affected = fbAdSetsMapper.batchInsertOrUpdate(batch);
                log.debug("全部广告信息-批量保存广告组：{}/{}, 影响行数：{}", endIndex, total, affected);
                return affected;
            }, "批量保存广告组", processed, endIndex);
            
            processed = endIndex;
        }
    }

    /**
     * 批量保存广告（带死锁重试机制）
     *
     * @param ads 广告列表
     */
    private void batchSaveAds(List<FbAdDO> ads) {
        int total = ads.size();
        int processed = 0;
        
        while (processed < total) {
            int endIndex = Math.min(processed + BATCH_SIZE, total);
            List<FbAdDO> batch = ads.subList(processed, endIndex);
            
            // 执行带重试的批量保存
            executeWithDeadlockRetry(() -> {
                int affected = fbAdMapper.batchInsertOrUpdate(batch);
                log.debug("全部广告信息-批量保存广告：{}/{}, 影响行数：{}", endIndex, total, affected);
                return affected;
            }, "批量保存广告", processed, endIndex);
            
            processed = endIndex;
        }
    }


    /**
     * 执行数据库操作，带死锁重试机制
     *
     * @param operation 数据库操作
     * @param operationName 操作名称（用于日志）
     * @param startIndex 开始索引
     * @param endIndex 结束索引
     * @return 操作结果
     */
    private <T> T executeWithDeadlockRetry(java.util.function.Supplier<T> operation,
                                           String operationName, int startIndex, int endIndex) {
        int retryCount = 0;

        while (retryCount < MAX_DEADLOCK_RETRIES) {
            try {
                return operation.get();
            } catch (org.springframework.dao.CannotAcquireLockException e) {
                retryCount++;

                // 指数退避延迟
                long delay = BASE_RETRY_DELAY * (1L << (retryCount - 1));
                log.warn("全部广告信息-{}检测到死锁，范围：{}-{}，第{}次重试，延迟{}ms",
                        operationName, startIndex, endIndex, retryCount, delay);

                try {
                    Thread.sleep(delay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试过程中被中断", ie);
                }
            } catch (org.springframework.dao.DataAccessException e) {
                // 处理其他数据库异常，包括死锁相关异常
                if (e.getMessage() != null && e.getMessage().toLowerCase().contains("deadlock")) {
                    retryCount++;

                    // 指数退避延迟
                    long delay = BASE_RETRY_DELAY * (1L << (retryCount - 1));
                    log.warn("全部广告信息-{}检测到死锁，范围：{}-{}，第{}次重试，延迟{}ms",
                            operationName, startIndex, endIndex, retryCount, delay);

                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("重试过程中被中断", ie);
                    }
                } else {
                    // 非死锁相关的数据库异常，直接抛出
                    log.error("全部广告信息-{}失败，范围：{}-{}", operationName, startIndex, endIndex, e);
                    throw e;
                }
            } catch (Exception e) {
                log.error("全部广告信息-{}失败，范围：{}-{}", operationName, startIndex, endIndex, e);
                throw e;
            }
        }

        log.error("全部广告信息-{}失败，范围：{}-{}，已达到最大重试次数：{}", operationName, startIndex, endIndex, MAX_DEADLOCK_RETRIES);
        // 理论上不会到达这里
        throw new RuntimeException("未知错误：已达到最大重试次数");
    }
}

    
