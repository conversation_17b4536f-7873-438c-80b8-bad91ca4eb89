/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service;

import org.springframework.web.multipart.MultipartFile;
import top.continew.admin.biz.enums.ExcelTemplateEnum;
import top.continew.admin.biz.model.entity.FbAccountDO;
import top.continew.admin.biz.model.req.FbAccountBatchUpdateRemarkReq;
import top.continew.admin.biz.model.req.FbAccountBatchUpdateStatusReq;
import top.continew.admin.biz.model.req.FbAccountBatchVerifyReq;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.FbAccountQuery;
import top.continew.admin.biz.model.req.FbAccountReq;
import top.continew.admin.biz.model.resp.FbAccountDetailResp;
import top.continew.admin.biz.model.resp.FbAccountResp;

import java.math.BigDecimal;
import java.util.List;

/**
 * fb账号业务接口
 *
 * <AUTHOR>
 * @since 2025/01/03 16:32
 */
public interface FbAccountService extends BaseService<FbAccountResp, FbAccountDetailResp, FbAccountQuery, FbAccountReq>, IService<FbAccountDO> {

    FbAccountDO getFbAccountByFbAccountId(String fbAccountId);

    List<String> importExcelBySystemIp(MultipartFile file, Long channelId, String tag, BigDecimal price, ExcelTemplateEnum type);

    List<String> importExcelByCustomIp(MultipartFile file, Long channelId, String tag, BigDecimal price,ExcelTemplateEnum type);

    void batchUpdateStatus(FbAccountBatchUpdateStatusReq req);

    void batchVerify(FbAccountBatchVerifyReq req);

    void batchUpdateRemark(FbAccountBatchUpdateRemarkReq req);
}