package top.continew.admin.biz.service;

import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.model.entity.CardBinDO;
import top.continew.admin.biz.model.query.CardBinQuery;
import top.continew.admin.biz.model.req.CardBinReq;
import top.continew.admin.biz.model.resp.CardBinDetailResp;
import top.continew.admin.biz.model.resp.CardBinResp;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;

/**
 * 卡头管理业务接口
 *
 * <AUTHOR>
 * @since 2025/09/02 15:44
 */
public interface CardBinService extends BaseService<CardBinResp, CardBinDetailResp, CardBinQuery, CardBinReq>, IService<CardBinDO> {


    void syncCardBin(CardPlatformEnum platform);

}