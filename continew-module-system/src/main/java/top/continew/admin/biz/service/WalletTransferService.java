package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.WalletTransferDO;
import top.continew.admin.biz.model.query.WalletTransferQuery;
import top.continew.admin.biz.model.req.WalletTransferReq;
import top.continew.admin.biz.model.resp.WalletTransferDetailResp;
import top.continew.admin.biz.model.resp.WalletTransferResp;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;

import java.math.BigDecimal;

/**
 * 钱包流水业务接口
 *
 * <AUTHOR>
 * @since 2025/07/22 15:59
 */
public interface WalletTransferService extends BaseService<WalletTransferResp, WalletTransferDetailResp, WalletTransferQuery, WalletTransferReq>, IService<WalletTransferDO> {

    void syncData(Long startTimestamp, Long endTimestamp);


    BigDecimal getCurrentBalance();
}