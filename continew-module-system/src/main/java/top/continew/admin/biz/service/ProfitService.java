package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.ProfitDO;
import top.continew.admin.biz.model.query.ProfitStatQuery;
import top.continew.admin.biz.model.resp.ProfitStatByDateResp;
import top.continew.admin.biz.model.resp.ProfitStatByTypeResp;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.ProfitQuery;
import top.continew.admin.biz.model.req.ProfitReq;
import top.continew.admin.biz.model.resp.ProfitDetailResp;
import top.continew.admin.biz.model.resp.ProfitResp;

import java.util.List;

/**
 * 利润业务接口
 *
 * <AUTHOR>
 * @since 2025/08/16 16:14
 */
public interface ProfitService extends BaseService<ProfitResp, ProfitDetailResp, ProfitQuery, ProfitReq>, IService<ProfitDO> {
    PageResp<ProfitStatByDateResp> selectStatByDatePage(ProfitStatQuery query, PageQuery pageQuery);

    List<ProfitStatByTypeResp> selectStatByTypeList(ProfitStatQuery query, String[] sort);

    List<ProfitStatByDateResp> selectStatByDateList(ProfitStatQuery query);
}