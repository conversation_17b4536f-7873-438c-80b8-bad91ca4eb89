/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service;

import top.continew.admin.biz.enums.CustomerBalanceTypeEnum;
import top.continew.admin.biz.model.entity.CustomerBalanceRecordDO;
import top.continew.admin.biz.model.query.CustomerBalanceRecordQuery;
import top.continew.admin.biz.model.req.CustomerBalanceRecordReq;
import top.continew.admin.biz.model.resp.CustomerBalanceRecordDetailResp;
import top.continew.admin.biz.model.resp.CustomerBalanceRecordResp;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.BasePageResp;
import top.continew.starter.extension.crud.service.BaseService;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 客户余额变更记录业务接口
 *
 * <AUTHOR>
 * @since 2024/12/31 09:27
 */
public interface CustomerBalanceRecordService extends BaseService<CustomerBalanceRecordResp, CustomerBalanceRecordDetailResp, CustomerBalanceRecordQuery, CustomerBalanceRecordReq>, IService<CustomerBalanceRecordDO> {

//    @Override
//    BasePageResp<CustomerBalanceRecordResp> page(CustomerBalanceRecordQuery query, PageQuery pageQuery);

    /**
     * 获取某个类型的总计金额
     *
     * @param customerId
     * @param typeEnum
     * @return
     */
    BigDecimal getTotalAmountByType(Long customerId, CustomerBalanceTypeEnum typeEnum);

    /**
     * 校正交易后余额
     *
     * @param customerId
     * @param recordId
     */
    void adjustAfterAmount(Long customerId, Long recordId);

    /**
     * 获取广告户总充值金额
     *
     * @param customerId
     * @param platformAdId
     * @param orderTime
     * @return
     */
    BigDecimal getTotalRechargeAmount(Long customerId, String platformAdId, LocalDateTime orderTime);

    /**
     * 上传水单
     *
     * @param id
     * @param fileUrl
     */
    void uploadCertificate(String id, String fileUrl);

    /**
     * 绑定钱包交易ID
     *
     * @param id
     * @param transactionId
     */
    void bindWalletTransId(Long id, String transactionId);

    /**
     * 获取总打款金额
     *
     * @param customerId
     * @return
     */
    BigDecimal getTotalTransferAmount(Long customerId);

    /**
     * 获取广告户总充值
     *
     * @param customerId
     * @param platformAdId
     * @param orderTime
     * @return
     */
    BigDecimal getAdAccountTotalRechargeAmount(Long customerId, String platformAdId, LocalDateTime orderTime);
}