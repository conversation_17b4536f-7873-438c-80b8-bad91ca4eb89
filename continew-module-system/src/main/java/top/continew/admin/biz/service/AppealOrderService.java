/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.AppealOrderDO;
import top.continew.admin.biz.model.query.AppealOrderQuery;
import top.continew.admin.biz.model.req.AppealOrderFinishReq;
import top.continew.admin.biz.model.req.AppealOrderReq;
import top.continew.admin.biz.model.req.AppealOrderUpdateRemarkReq;
import top.continew.admin.biz.model.resp.AppealOrderDetailResp;
import top.continew.admin.biz.model.resp.AppealOrderResp;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;

/**
 * 申诉订单业务接口
 *
 * <AUTHOR>
 * @since 2025/01/16 11:24
 */
public interface AppealOrderService extends BaseService<AppealOrderResp, AppealOrderDetailResp, AppealOrderQuery, AppealOrderReq>, IService<AppealOrderDO> {

    /**
     * 处理订单
     *
     * @param id
     */
    void handleOrder(Long id);

    /**
     * 完成订单
     *
     * @param req
     */
    void finishOrder(Long id, AppealOrderFinishReq req);

    /**
     * 取消订单
     *
     * @param id
     */
    void cancelOrder(Long id);

    /**
     * 提交清零
     *
     * @param id
     */
    void applyClear(Long id);

    /**
     * 修改备注
     *
     * @param req
     */
    void updateRemark(AppealOrderUpdateRemarkReq req);
}