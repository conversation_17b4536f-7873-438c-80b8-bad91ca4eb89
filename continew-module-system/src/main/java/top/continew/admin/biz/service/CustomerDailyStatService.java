package top.continew.admin.biz.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.CustomerDailyStatDO;
import top.continew.admin.biz.model.query.CustomerDailyStatQuery;
import top.continew.admin.biz.model.req.CustomerDailyStatReq;
import top.continew.admin.biz.model.resp.CustomerDailyStatByDateResp;
import top.continew.admin.biz.model.resp.CustomerDailyStatDetailResp;
import top.continew.admin.biz.model.resp.CustomerDailyStatResp;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseService;

import java.time.LocalDate;
import java.util.List;

/**
 * 客户每日统计业务接口
 *
 * <AUTHOR>
 * @since 2025/07/17 10:11
 */
public interface CustomerDailyStatService extends BaseService<CustomerDailyStatResp, CustomerDailyStatDetailResp, CustomerDailyStatQuery, CustomerDailyStatReq>, IService<CustomerDailyStatDO> {

    /**
     * 统计数据
     *
     * @param date
     */
    void statData(LocalDate date);


    PageResp<CustomerDailyStatByDateResp> selectDailyPage(CustomerDailyStatQuery query, PageQuery pageQuery);

    List<CustomerDailyStatByDateResp> selectDailyList(CustomerDailyStatQuery query);
}