package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 卡头管理查询条件
 *
 * <AUTHOR>
 * @since 2025/09/02 15:44
 */
@Data
@Schema(description = "卡头管理查询条件")
public class CardBinQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 卡头号码
     */
    @Schema(description = "卡头号码")
    @Query(type = QueryType.EQ)
    private String cardBin;

    /**
     * 卡头名称
     */
    @Schema(description = "卡头名称")
    @Query(type = QueryType.EQ)
    private String name;

    /**
     * 所属平台
     */
    @Schema(description = "所属平台")
    @Query(type = QueryType.EQ)
    private Integer platform;

    /**
     * 卡组织
     */
    @Schema(description = "卡组织")
    @Query(type = QueryType.EQ)
    private String cardScheme;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    @Query(type = QueryType.EQ)
    private Boolean enable;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @Query(type = QueryType.EQ)
    private LocalDateTime createTime;
}