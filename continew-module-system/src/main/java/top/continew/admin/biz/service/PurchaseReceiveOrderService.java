package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.PurchaseReceiveOrderDO;
import top.continew.admin.biz.model.query.PurchaseReceiveOrderQuery;
import top.continew.admin.biz.model.req.PurchaseReceiveOrderReq;
import top.continew.admin.biz.model.resp.PurchaseReceiveOrderDetailResp;
import top.continew.admin.biz.model.resp.PurchaseReceiveOrderResp;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购验收单业务接口
 *
 * <AUTHOR>
 * @since 2025/05/21 14:38
 */
public interface PurchaseReceiveOrderService extends BaseService<PurchaseReceiveOrderResp, PurchaseReceiveOrderDetailResp, PurchaseReceiveOrderQuery, PurchaseReceiveOrderReq>, IService<PurchaseReceiveOrderDO> {

    List<PurchaseReceiveOrderResp> selectPurchaseTotal(LocalDateTime[] createTime);
}