package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CustomerPerformanceStatResp {

    @Schema(description = "商务人员ID")
    private Long businessUserId;

    @Schema(description = "商务人员名称")
    private String businessUserName;

    @Schema(description = "有效拉群数")
    private Integer validGroupCount;

    @Schema(description = "首充新客户数")
    private Integer firstRechargeNewCustomerCount;

    @Schema(description = "首月消耗达标新客户数：(3000, 1w] 区间")
    private Integer firstMonthConsumptionQualifiedNewCustomerCount3KTo10K = 0;

    @Schema(description = "首月消耗达标新客户数：(1w, 3w] 区间")
    private Integer firstMonthConsumptionQualifiedNewCustomerCount10KTo30K = 0;

    @Schema(description = "首月消耗达标新客户数：(3w, 5w] 区间")
    private Integer firstMonthConsumptionQualifiedNewCustomerCount30KTo50K = 0;

}
