package top.continew.admin.biz.service.impl;

import cn.hutool.core.map.MapUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.katai.CardOpsStrategyFactory;
import top.continew.admin.biz.katai.strategy.CardOpsStrategy;
import top.continew.admin.biz.mapper.CardBinMapper;
import top.continew.admin.biz.model.entity.CardBinDO;
import top.continew.admin.biz.model.query.CardBinQuery;
import top.continew.admin.biz.model.req.CardBinReq;
import top.continew.admin.biz.model.resp.CardBinDetailResp;
import top.continew.admin.biz.model.resp.CardBinResp;
import top.continew.admin.biz.service.CardBinService;
import top.continew.starter.extension.crud.model.resp.LabelValueResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 卡头管理业务实现
 *
 * <AUTHOR>
 * @since 2025/09/02 15:44
 */
@Service
@RequiredArgsConstructor
public class CardBinServiceImpl extends BaseServiceImpl<CardBinMapper, CardBinDO, CardBinResp, CardBinDetailResp, CardBinQuery, CardBinReq> implements CardBinService {

    private final CardOpsStrategyFactory cardOpsStrategyFactory;

    @Override
    public void syncCardBin(CardPlatformEnum platform) {
        CardOpsStrategy<?> cardOpsStrategy = cardOpsStrategyFactory.findStrategy(platform);
        List<LabelValueResp<String>> cardBinList = cardOpsStrategy.getCardBinList();
        LabelValueResp<String> item = new LabelValueResp<>();
        item.setLabel("123");
        item.setLabel("123");
        Map<String, Object> map = new HashMap<>();
        map.put("cardScheme", "visa");
        item.setExtra(map);
        cardBinList.add(item);


        List<String> binlist = new LinkedList<>();
        Map<String, String> cardSchemeMap = new HashMap<>();
        for (LabelValueResp<String> res : cardBinList) {
            if (!res.getValue().equals("AUTO")) {
                binlist.add(res.getValue());
                Object extraObj = res.getExtra();
                if (extraObj instanceof Map) {
                    Map<String, Object> extraMap = (Map<String, Object>) extraObj;
                    String cardScheme = MapUtil.getStr(extraMap, "cardScheme", "");
                    cardSchemeMap.put(res.getValue(), cardScheme);
                }
            }
        }
        for (String bin : binlist) {
            CardBinDO cardBinDO = new CardBinDO();
            cardBinDO.setCardBin(bin);
            cardBinDO.setPlatform(platform);
            cardBinDO.setName(bin);
            cardBinDO.setCardScheme(cardSchemeMap.getOrDefault(bin, ""));
            try {
                save(cardBinDO);
            } catch (DuplicateKeyException e) {
                // ignore
            }
        }
    }
}