package top.continew.admin.biz.service;

import top.continew.admin.biz.enums.TransactionUserTypeEnum;
import top.continew.admin.biz.model.entity.TransactionUserDO;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.TransactionUserQuery;
import top.continew.admin.biz.model.req.TransactionUserReq;
import top.continew.admin.biz.model.resp.TransactionUserDetailResp;
import top.continew.admin.biz.model.resp.TransactionUserResp;

/**
 * 交易对象业务接口
 *
 * <AUTHOR>
 * @since 2025/08/17 14:22
 */
public interface TransactionUserService extends BaseService<TransactionUserResp, TransactionUserDetailResp, TransactionUserQuery, TransactionUserReq> {

    Long getUser(Long referId, TransactionUserTypeEnum type, String name);

    void updateTransactionUserName(TransactionUserTypeEnum type, Long referId, String name);

}