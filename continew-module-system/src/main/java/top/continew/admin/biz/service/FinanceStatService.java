/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service;

import org.springframework.web.multipart.MultipartFile;
import top.continew.admin.biz.model.query.AdAccountInsightQuery;
import top.continew.admin.biz.model.query.AdAccountRetentionQuery;
import top.continew.admin.biz.model.query.CustomerStatReportQuery;
import top.continew.admin.biz.model.resp.AdAccountDailyStatReportResp;
import top.continew.admin.biz.model.resp.AdAccountRetentionResp;
import top.continew.admin.biz.model.resp.AdAccountStatResp;
import top.continew.admin.biz.model.resp.DailyStatReportResp;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.BasePageResp;
import top.continew.starter.extension.crud.model.resp.PageResp;

import java.util.List;

public interface FinanceStatService {

    /**
     * 导入数日报数据
     *
     * @param customerId
     * @param file
     */
    void importDailyReport(Long customerId, MultipartFile file);

    /**
     * 广告户报告分页查询
     *
     * @param query
     * @param pageQuery
     * @return
     */
    BasePageResp<AdAccountStatResp> pageAdAccountStatReport(AdAccountInsightQuery query, PageQuery pageQuery);

    /**
     * 广告户报告-每日数据分页查询
     *
     * @param query
     * @param pageQuery
     * @return
     */
    BasePageResp<AdAccountDailyStatReportResp> pageAdAccountDailyStatReport(AdAccountInsightQuery query,
                                                                            PageQuery pageQuery);


    BasePageResp<AdAccountRetentionResp> pageAdAccountRetention(AdAccountRetentionQuery query, PageQuery pageQuery);
    

    /**
     * 查询每日统计数据
     *
     * @param query
     * @param pageQuery
     * @return
     */
    PageResp<DailyStatReportResp> selectDailyStatReportPage(CustomerStatReportQuery query, PageQuery pageQuery);


    List<DailyStatReportResp> selectDailyStatReportList(CustomerStatReportQuery query);
}
