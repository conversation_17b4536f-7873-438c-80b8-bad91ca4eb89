package top.continew.admin.biz.service;

import top.continew.admin.biz.model.query.AdAccountSpentQuery;
import top.continew.admin.biz.model.query.CustomerAnalyzeQuery;
import top.continew.admin.biz.model.resp.CustomerSpentResp;
import top.continew.admin.biz.model.resp.CustomerSpentStatisticsResp;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.BasePageResp;

import java.time.LocalDateTime;
import java.util.List;

public interface AdAccountSpentService {


    /**
     * 广告户消耗列表-客户
     * @param query
     * @param pageQuery
     * @return
     */
    BasePageResp<CustomerSpentResp> pageByCustomer(AdAccountSpentQuery query, PageQuery pageQuery);

    /**
     * 广告户消耗的汇总统计数据-客户
     * @param query
     * @return
     */
    CustomerSpentStatisticsResp getCustomerDashboardStats(AdAccountSpentQuery query);

    /**
     * 广告户消耗分析-客户
     * @param query
     * @return
     */
    List<CustomerSpentResp> analyzeByCustomer(CustomerAnalyzeQuery query);



    /**
     * 广告户消耗列表-广告户
     * @param query
     * @param pageQuery
     * @return
     */
    BasePageResp<CustomerSpentResp> pageByAdAccount(AdAccountSpentQuery query, PageQuery pageQuery);

    /**
     * 广告户消耗的汇总统计数据-广告户
     * @param query
     * @return
     */
    CustomerSpentStatisticsResp getAdAccountDashboardStats(AdAccountSpentQuery query);

    /**
     * 广告户消耗分析-广告户
     * @param query
     * @return
     */
    List<CustomerSpentResp> analyzeByAdAccount(CustomerAnalyzeQuery query);


    LocalDateTime[] timeInterval(String adAccountIds);
}
