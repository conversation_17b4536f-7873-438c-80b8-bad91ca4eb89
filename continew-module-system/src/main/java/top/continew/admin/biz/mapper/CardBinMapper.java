package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.query.CardBinQuery;
import top.continew.admin.biz.model.resp.CardBinResp;
import top.continew.starter.data.mp.base.BaseMapper;
import top.continew.admin.biz.model.entity.CardBinDO;

/**
 * 卡头管理 Mapper
 *
 * <AUTHOR>
 * @since 2025/09/02 15:44
 */
public interface CardBinMapper extends BaseMapper<CardBinDO> {

    IPage<CardBinResp> customerPage(@Param("page") Page<CardBinResp> objectPage, @Param("query") CardBinQuery query);
}