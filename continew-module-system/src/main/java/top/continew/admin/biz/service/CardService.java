/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service;

import com.alicp.jetcache.anno.Cached;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.model.entity.CardDO;
import top.continew.admin.biz.model.query.CardQuery;
import top.continew.admin.biz.model.req.CardOpenReq;
import top.continew.admin.biz.model.req.CardPlatformUpdateReq;
import top.continew.admin.biz.model.req.CardRechargeReq;
import top.continew.admin.biz.model.req.CardReq;
import top.continew.admin.biz.model.resp.CardDetailResp;
import top.continew.admin.biz.model.resp.CardResp;
import top.continew.admin.common.constant.CacheConstants;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.model.resp.LabelValueResp;
import top.continew.starter.extension.crud.service.BaseService;

import java.math.BigDecimal;
import java.util.List;

/**
 * 卡片业务接口
 *
 * <AUTHOR>
 * @since 2024/12/28 10:43
 */
public interface CardService extends BaseService<CardResp, CardDetailResp, CardQuery, CardReq>, IService<CardDO> {

    /**
     * 查询平台卡片
     *
     * @param platform
     * @return
     */
    List<CardDO> listByPlatform(Integer platform);

    /**
     * 同步卡片数据
     */
    void syncData(Integer limitPage);

    /**
     * 获取卡片信息
     *
     * @param cardNumber
     * @param ignoreNull
     * @return
     */
    CardDO getByCardNumber(String cardNumber, boolean ignoreNull);

    @Cached(key = "#cardNumber", name = CacheConstants.CARD_KEY_PREFIX, expire = 7200)
    CardDO getByCardNumberByCache(String cardNumber);

    /**
     * 获取卡台余额
     *
     * @param platform
     * @return
     */
    BigDecimal getPlatformBalance(CardPlatformEnum platform);

    /**
     * 获取卡片总余额
     *
     * @return
     */
    BigDecimal getCardTotalBalance();

    /**
     * 获取卡片授权验证码
     *
     * @param cardNumber
     * @return
     */
    String getCardVerifyCode(String cardNumber);

    /**
     * 更新备注
     *
     * @param req
     */
    void updateRemark(CardPlatformUpdateReq req);

    /**
     * 充值金额
     *
     * @param req
     */
    void recharge(CardRechargeReq req);

    /**
     * 获取卡头
     *
     * @param platformEnum
     * @return
     */
    List<LabelValueResp<String>> getCardBinList(CardPlatformEnum platformEnum);

    /**
     * 开卡
     *
     * @param req
     * @return
     */
    CardDO open(CardOpenReq req);

    CardDO getInterlaceUnUsedCard(CardPlatformEnum platformEnum, String cardBin);

    /**
     * 获取申请卡的结果
     *
     * @param platformEnum
     * @param requestId
     * @return
     */
    CardDO getApplyCardResult(CardPlatformEnum platformEnum, String requestId);

    /**
     * 获取完整卡号
     *
     * @param platformEnum
     * @param platformCardId
     * @return
     */
    String getCardNumber(CardPlatformEnum platformEnum, String platformCardId);

    /**
     * 获取完整卡号
     *
     * @param platformEnum
     * @param platformCardId
     * @return
     */
    CardDO getByPlatformCardId(CardPlatformEnum platformEnum, String platformCardId);

    /**
     * 处理卡片的敏感信息
     */
    void loadCardSensitiveInfo(CardPlatformEnum platformEnum);

    /**
     * 处理卡商的卡片的消耗金额字段
     */
    void updateCardUsedAmount(CardPlatformEnum platform);

    void loadGzyCardHolderId();

    /**
     * 获取需要提现的卡片
     *
     * @return
     */
    List<CardResp> getNeedWithdrawCard();

    void active(Long id);

    void inactive(Long id);

    void active(String cardNumber);

    void inactive(String cardNumber);

    void addCardBalance(Long id, BigDecimal balance);

    void subCardBalance(Long id, BigDecimal balance);

    /**
     * 同步卡片余额
     *
     * @param id
     */
    void syncCardBalance(Long id);

    List<String> cardHeadList();

    /**
     * 更新卡片使用状态
     *
     * @param id      卡片ID
     * @param hasUsed 是否已使用
     */
    void updateUsedStatus(Long id, Boolean hasUsed);


}