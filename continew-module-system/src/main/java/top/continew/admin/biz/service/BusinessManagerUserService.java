package top.continew.admin.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import top.continew.admin.biz.model.entity.BusinessManagerUserDO;
import top.continew.admin.biz.model.query.BusinessManagerUserQuery;
import top.continew.admin.biz.model.req.BusinessManagerUserReq;
import top.continew.admin.biz.model.resp.BusinessManagerUserDetailResp;
import top.continew.admin.biz.model.resp.BusinessManagerUserResp;
import top.continew.starter.extension.crud.service.BaseService;

/**
 * bm管理员业务接口
 *
 * <AUTHOR>
 * @since 2025/04/22 16:45
 */
public interface BusinessManagerUserService extends BaseService<BusinessManagerUserResp, BusinessManagerUserDetailResp, BusinessManagerUserQuery, BusinessManagerUserReq>, IService<BusinessManagerUserDO> {

    /**
     * 加入白名单
     *
     * @param email
     */
    void addWhiteList(String email);
}