package top.continew.admin.biz.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.FbAdOptimizationGoalEnum;
import top.continew.admin.biz.mapper.FbAdSetsMapper;
import top.continew.admin.biz.model.entity.CampaignInsightDO;
import top.continew.admin.biz.model.entity.FbAdSetsDO;
import top.continew.admin.biz.utils.RabbitUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 广告户成效同步服务
 *
 * <AUTHOR>
 * @since 2025/01/03 14:26
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AdAccountEffectiveSyncService {

    private final CampaignInsightService campaignInsightService;
    private final FbAdSetsMapper fbAdSetsMapper;

    /**
     * 批量处理大小，避免单次操作数据量过大
     */
    private static final int BATCH_SIZE = 200;

    /**
     * 最大重试次数
     */
    private static final int MAX_DEADLOCK_RETRIES = 3;

    /**
     * 基础重试延迟（毫秒）
     */
    private static final long BASE_RETRY_DELAY = 100;

    /**
     * 获取并同步广告组成效数据
     *
     * @param serialNumber 序列号
     * @param proxy        代理
     * @param headers      请求头
     * @param isLite       是否轻量级
     * @param startDate    开始日期
     * @param endDate      结束日期
     */
    public void syncAdSetsEffectiveData(String serialNumber, String proxy, String headers, boolean isLite, String startDate, String endDate) {
        String response = RabbitUtils.getAdInfosByTimeRangeV2(serialNumber, isLite, startDate, endDate, proxy, headers);
        String browserKey = StringUtils.defaultIfBlank("", serialNumber);

        try {
            JSONObject jsonObject = JSONObject.parseObject(response);
            //先判断code是不是整数，如果不是则也是失败，其可能是ECONNREFUSED
            Integer code;
            try {
                code = jsonObject.getInteger("code");
            } catch (Exception e) {
                log.error("广告组成效数据-【{}】解析响应code失败，可能是网络错误: {}", browserKey, response);
                return;
            }

            if (code == null || code != 0) {
                log.error("广告组成效数据-【{}】请求失败，响应code: {}, 响应内容: {}", browserKey, code, response);
                return;
            }

            JSONArray jsonArray = jsonObject.getJSONArray("data");
            if (jsonArray == null || jsonArray.isEmpty()) {
                log.debug("广告组成效数据-【{}】没有数据需要同步", browserKey);
                return;
            }

            // 统计处理结果
            int totalAccounts = jsonArray.size();
            int successCount = 0;
            int errorCount = 0;

            //log.info("广告组成效数据-【{}】开始处理 {} 个广告账户的成效数据", browserKey, totalAccounts);

            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject dataItem = jsonArray.getJSONObject(i);
                try {
                    boolean success = processAccountEffectiveData(dataItem, browserKey);
                    if (success) {
                        successCount++;
                    } else {
                        errorCount++;
                    }
                } catch (Exception e) {
                    errorCount++;
                    log.error("广告组成效数据-【{}】处理第 {} 个账户数据时发生异常", browserKey, i + 1, e);
                }
            }

            log.debug("广告组成效数据-【{}】成效数据同步完成，总计：{}，成功：{}，失败：{}",
                    browserKey, totalAccounts, successCount, errorCount);

        } catch (Exception e) {
            log.error("广告组成效数据-【{}】解析成效数据响应时发生异常", browserKey, e);
            throw new RuntimeException("广告组成效数据同步失败", e);
        }
    }

    /**
     * 处理单个账户的成效数据
     *
     * @param dataItem   账户数据
     * @param browserKey 浏览器标识
     * @return 是否处理成功
     */
    private boolean processAccountEffectiveData(JSONObject dataItem, String browserKey) {
        int itemCode = dataItem.getIntValue("code");
        JSONObject item = dataItem.getJSONObject("body");

        if (item == null) {
            log.warn("广告组成效数据-【{}】账户数据为空", browserKey);
            return false;
        }

        String adAccountId = item.getString("account_id");

        if (itemCode != 200) {
            log.error("广告组成效数据-【{}】{}同步失败：{}", browserKey, adAccountId, item);
            return false;
        }

        // 获取广告系列数据
        JSONArray campaigns = item.getJSONArray("campaigns");
        if (campaigns == null || campaigns.isEmpty()) {
            log.warn("广告组成效数据-【{}】{}没有广告系列数据", browserKey, adAccountId);
            return true;
        }

        try {
            // 解析并批量保存成效数据
            parseAndSaveEffectiveData(adAccountId, campaigns);
            return true;

        } catch (Exception e) {
            log.error("广告组成效数据-【{}】{}处理成效数据时发生异常", browserKey, adAccountId, e);
            return false;
        }
    }

    /**
     * 解析并批量保存成效数据
     *
     * @param adAccountId 广告账户ID
     * @param campaigns   广告系列数据
     */
    private void parseAndSaveEffectiveData(String adAccountId, JSONArray campaigns) {
        List<CampaignInsightDO> insights = new ArrayList<>();

        for (int i = 0; i < campaigns.size(); i++) {
            JSONObject campaignJson = campaigns.getJSONObject(i);
            String campaignId = campaignJson.getString("id");

            // 获取广告组数据
            JSONArray adSets = campaignJson.getJSONArray("adsets");
            if (adSets == null || adSets.isEmpty()) {
                log.warn("广告组成效数据-{}-{} 没有广告组数据", adAccountId, campaignId);
                continue;
            }

            for (int j = 0; j < adSets.size(); j++) {
                JSONObject adSetJson = adSets.getJSONObject(j);

                // 解析广告组成效数据（支持多日期）
                List<CampaignInsightDO> adSetInsights = parseAdSetEffectiveData(adSetJson, adAccountId, campaignId);
                if (adSetInsights != null && !adSetInsights.isEmpty()) {
                    insights.addAll(adSetInsights);
                }
            }
        }

        // 批量保存成效数据
        if (!insights.isEmpty()) {
            batchSaveInsights(insights);
            log.debug("广告组成效数据-账户 {} 成效数据同步完成：{} 条记录", adAccountId, insights.size());
        } else {
            log.warn("广告组成效数据-账户 {} 没有有效的成效数据", adAccountId);
        }
    }

    /**
     * 解析广告组成效数据（支持多日期）
     *
     * @param adSetJson   广告组JSON数据
     * @param adAccountId 广告账户ID
     * @param campaignId  广告系列ID
     * @return 成效数据实体列表
     */
    private List<CampaignInsightDO> parseAdSetEffectiveData(JSONObject adSetJson, String adAccountId, String campaignId) {
        String adSetId = adSetJson.getString("id");
        List<CampaignInsightDO> insights = new ArrayList<>();

        // 检查是否有新的insights结构
        JSONArray insightsArray = adSetJson.getJSONArray("insights");
        if (insightsArray != null && !insightsArray.isEmpty()) {
            // 新结构：解析insights数组中的多日期数据
            for (int i = 0; i < insightsArray.size(); i++) {
                JSONObject insightJson = insightsArray.getJSONObject(i);
                CampaignInsightDO insight = parseSingleInsightData(insightJson, adAccountId, campaignId, adSetId);
                if (insight != null) {
                    insights.add(insight);
                }
            }
        } else {
            // 兼容旧结构：直接解析广告组数据
            if (!adSetJson.containsKey("impressions") && !adSetJson.containsKey("clicks") && !adSetJson.containsKey("spend")) {
                log.debug("广告组成效数据-{}-{}-{} 没有成效数据字段", adAccountId, campaignId, adSetId);
                return insights;
            }
            
            CampaignInsightDO insight = parseSingleInsightData(adSetJson, adAccountId, campaignId, adSetId);
            if (insight != null) {
                insights.add(insight);
            }
        }
        
        return insights;
    }
    
    /**
     * 解析单个成效数据
     *
     * @param insightJson 成效JSON数据
     * @param adAccountId 广告账户ID
     * @param campaignId  广告系列ID
     * @param adSetId     广告组ID
     * @return 成效数据实体
     */
    private CampaignInsightDO parseSingleInsightData(JSONObject insightJson, String adAccountId, String campaignId, String adSetId) {
        CampaignInsightDO insight = new CampaignInsightDO();
        insight.setPlatformCampaignId(campaignId);
        insight.setPlatformAdsetId(adSetId);
        insight.setAdAccountId(adAccountId);
        
        // 解析日期信息（优先使用insights中的日期）
        String dateStart = insightJson.getString("date_start");
        if (StringUtils.isNotBlank(dateStart)) {
            insight.setStatDate(LocalDate.parse(dateStart));
        } else {
            return null;
        }
        
        // 解析基础成效数据
        String impressionsStr = insightJson.getString("impressions");
        if (StringUtils.isNotBlank(impressionsStr)) {
            try {
                insight.setImpressions(Long.valueOf(impressionsStr));
            } catch (NumberFormatException e) {
                log.warn("广告组成效数据-解析展示次数失败：{}", impressionsStr);
                insight.setImpressions(0L);
            }
        } else {
            insight.setImpressions(0L);
        }
        
        String clicksStr = insightJson.getString("clicks");
        if (StringUtils.isNotBlank(clicksStr)) {
            try {
                insight.setClicks(Long.valueOf(clicksStr));
            } catch (NumberFormatException e) {
                log.warn("广告组成效数据-解析点击次数失败：{}", clicksStr);
                insight.setClicks(0L);
            }
        } else {
            insight.setClicks(0L);
        }
        
        String spendStr = insightJson.getString("spend");
        if (StringUtils.isNotBlank(spendStr)) {
            try {
                insight.setSpend(new BigDecimal(spendStr));
            } catch (NumberFormatException e) {
                log.warn("广告组成效数据-解析花费金额失败：{}", spendStr);
                insight.setSpend(BigDecimal.ZERO);
            }
        } else {
            insight.setSpend(BigDecimal.ZERO);
        }
        
        // 查询广告组的优化目标和事件类型
        FbAdSetsDO adSetInfo = fbAdSetsMapper.selectOptimizationInfoByPlatformId(adSetId);
        String optimizationGoal = adSetInfo != null ? adSetInfo.getOptimizationGoal() : null;
        String customEventType = adSetInfo != null ? adSetInfo.getCustomEventType() : null;
        
        // 解析转化数据
        ConversionMatchResult matchResult = parseConversionDataWithCustomEventSupport(
                insightJson, optimizationGoal, customEventType, adSetId, adAccountId);
        
        // 设置转化数据
        if (!matchResult.conversionsMap.isEmpty()) {
            insight.setConversionsData(JSONObject.toJSONString(matchResult.conversionsMap));
        }
        if (!matchResult.actionsMap.isEmpty()) {
            insight.setActionsData(JSONObject.toJSONString(matchResult.actionsMap));
        }
        
        // 设置转化成效值和成效指标key
        insight.setConversionValue(matchResult.conversionValue);
        insight.setConversionKey(matchResult.conversionKey);
        
        // 设置创建和更新时间
        LocalDateTime now = LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS);
        insight.setCreateTime(now);
        insight.setUpdateTime(now);
        
        return insight;
    }

    /**
     * 批量保存成效数据（带死锁重试机制）
     *
     * @param insights 成效数据列表
     */
    private void batchSaveInsights(List<CampaignInsightDO> insights) {
        int total = insights.size();
        int processed = 0;
        
        while (processed < total) {
            int endIndex = Math.min(processed + BATCH_SIZE, total);
            List<CampaignInsightDO> batch = insights.subList(processed, endIndex);
            
            // 执行带重试的批量保存
            executeWithDeadlockRetry(() -> {
                campaignInsightService.batchSaveOrUpdate(batch);
                log.debug("广告组成效数据-批量保存成效数据：{}/{}", endIndex, total);
                return null;
            }, "批量保存成效数据", processed, endIndex);
            
            processed = endIndex;
        }
    }

    /**
     * 解析转化数据的通用方法
     *
     * @param dataArray 转化数据数组
     * @param goalEnum  优化目标枚举
     * @param adSetId   广告组ID
     * @param dataType  数据类型（conversions或actions）
     * @return 转化结果
     */
    private ConversionResult parseConversionData(JSONArray dataArray, FbAdOptimizationGoalEnum goalEnum, String adSetId, String dataType) {
        ConversionResult result = new ConversionResult();
        result.dataMap = new HashMap<>();
        result.foundMatch = false;
        
        for (int k = 0; k < dataArray.size(); k++) {
            JSONObject dataItem = dataArray.getJSONObject(k);
            String actionType = dataItem.getString("action_type");
            String value = dataItem.getString("value");
            
            if (StringUtils.isNotBlank(actionType) && StringUtils.isNotBlank(value)) {
                result.dataMap.put(actionType, value);
                
                // 如果找到了匹配的优化目标枚举，并且action_type匹配
                if (goalEnum != null && actionType.equals(goalEnum.getActionType())) {
                    try {
                        result.conversionValue = new BigDecimal(value);
                        result.conversionKey = actionType;
                        result.foundMatch = true;
                        log.debug("广告组成效数据-{} 从{}匹配到转化成效值：{} (action_type: {})", 
                                adSetId, dataType, result.conversionValue, actionType);
                    } catch (NumberFormatException e) {
                        log.warn("广告组成效数据-解析{}转化成效值失败：{}", dataType, value);
                    }
                }
            }
        }
        
        return result;
    }
    
    /**
     * 转化结果内部类
     */
    private static class ConversionResult {
        Map<String, String> dataMap;
        BigDecimal conversionValue;
        String conversionKey;
        boolean foundMatch;
    }
    
    /**
     * 转化匹配结果内部类
     */
    private static class ConversionMatchResult {
        Map<String, String> conversionsMap = new HashMap<>();
        Map<String, String> actionsMap = new HashMap<>();
        BigDecimal conversionValue = BigDecimal.ZERO;
        String conversionKey;
    }
    
    /**
     * 解析转化数据，支持自定义事件模糊匹配
     * 
     * @param adSetJson 广告组JSON数据
     * @param optimizationGoal 优化目标
     * @param customEventType 自定义事件类型
     * @param adSetId 广告组ID
     * @param adAccountId 广告账户ID
     * @return 转化匹配结果
     */
    private ConversionMatchResult parseConversionDataWithCustomEventSupport(
            JSONObject adSetJson, String optimizationGoal, String customEventType, 
            String adSetId, String adAccountId) {
        
        ConversionMatchResult result = new ConversionMatchResult();
        
        // 解析conversions和actions数据
        JSONArray conversions = adSetJson.getJSONArray("conversions");
        JSONArray actions = adSetJson.getJSONArray("actions");
        
        if (conversions != null && !conversions.isEmpty()) {
            result.conversionsMap = extractDataMap(conversions);
        }
        
        if (actions != null && !actions.isEmpty()) {
            result.actionsMap = extractDataMap(actions);
        }
        
        // 如果存在自定义事件类型，先尝试模糊匹配
        if (StringUtils.isNotBlank(customEventType)) {
            ConversionResult customEventResult = tryFuzzyMatchCustomEvent(
                    customEventType, conversions, actions, adSetId);
            
            if (customEventResult.foundMatch) {
                result.conversionValue = customEventResult.conversionValue;
                result.conversionKey = customEventResult.conversionKey;
                log.debug("广告组成效数据-{} 通过自定义事件模糊匹配到转化成效值：{} (conversion_key: {})", 
                        adSetId, result.conversionValue, result.conversionKey);
                return result;
            }
        }
        
        // 如果自定义事件没有匹配到，使用标准事件枚举进行精确匹配
        FbAdOptimizationGoalEnum goalEnum = FbAdOptimizationGoalEnum.findByGoalAndEvent(optimizationGoal, customEventType);
        if (goalEnum != null) {
            ConversionResult standardEventResult = tryExactMatchStandardEvent(
                    goalEnum, conversions, actions, adSetId);
            
            if (standardEventResult.foundMatch) {
                result.conversionValue = standardEventResult.conversionValue;
                result.conversionKey = standardEventResult.conversionKey;
                log.debug("广告组成效数据-{} 通过标准事件精确匹配到转化成效值：{} (conversion_key: {})", 
                        adSetId, result.conversionValue, result.conversionKey);
            } else {
                // 如果存在目标事件类型但没有找到匹配，记录警告日志
//                log.warn("[成效指标匹配失败] 广告户ID: {}, 广告组ID: {}, 优化目标: {}, 目标事件: {}, 期望actionType: {}, conversions中的actionTypes: {}, actions中的actionTypes: {}",
//                        adAccountId, adSetId, optimizationGoal, customEventType, goalEnum.getActionType(),
//                        result.conversionsMap.keySet(), result.actionsMap.keySet());
            }
        }
        
        return result;
    }
    
    /**
     * 提取数据映射
     * 
     * @param dataArray JSON数组
     * @return 数据映射
     */
    private Map<String, String> extractDataMap(JSONArray dataArray) {
        Map<String, String> dataMap = new HashMap<>();
        for (int i = 0; i < dataArray.size(); i++) {
            JSONObject dataItem = dataArray.getJSONObject(i);
            String actionType = dataItem.getString("action_type");
            String value = dataItem.getString("value");
            
            if (StringUtils.isNotBlank(actionType) && StringUtils.isNotBlank(value)) {
                dataMap.put(actionType, value);
            }
        }
        return dataMap;
    }
    
    /**
     * 尝试模糊匹配自定义事件
     * 
     * @param customEventType 自定义事件类型
     * @param conversions conversions数据
     * @param actions actions数据
     * @param adSetId 广告组ID
     * @return 转化结果
     */
    private ConversionResult tryFuzzyMatchCustomEvent(
            String customEventType, JSONArray conversions, JSONArray actions, String adSetId) {
        
        ConversionResult result = new ConversionResult();
        result.foundMatch = false;
        
        // 先在conversions中模糊匹配
        if (conversions != null && !conversions.isEmpty()) {
            ConversionResult conversionResult = fuzzyMatchInDataArray(conversions, customEventType, adSetId, "conversions");
            if (conversionResult.foundMatch) {
                return conversionResult;
            }
        }
        
        // 再在actions中模糊匹配
        if (actions != null && !actions.isEmpty()) {
            ConversionResult actionResult = fuzzyMatchInDataArray(actions, customEventType, adSetId, "actions");
            if (actionResult.foundMatch) {
                return actionResult;
            }
        }
        
        return result;
    }
    
    /**
     * 尝试精确匹配标准事件
     * 
     * @param goalEnum 目标枚举
     * @param conversions conversions数据
     * @param actions actions数据
     * @param adSetId 广告组ID
     * @return 转化结果
     */
    private ConversionResult tryExactMatchStandardEvent(
            FbAdOptimizationGoalEnum goalEnum, JSONArray conversions, JSONArray actions, String adSetId) {
        
        ConversionResult result = new ConversionResult();
        result.foundMatch = false;
        
        // 先在conversions中精确匹配
        if (conversions != null && !conversions.isEmpty()) {
            ConversionResult conversionResult = parseConversionData(conversions, goalEnum, adSetId, "conversions");
            if (conversionResult.foundMatch) {
                return conversionResult;
            }
        }
        
        // 再在actions中精确匹配
        if (actions != null && !actions.isEmpty()) {
            ConversionResult actionResult = parseConversionData(actions, goalEnum, adSetId, "actions");
            if (actionResult.foundMatch) {
                return actionResult;
            }
        }
        
        return result;
    }
    
    /**
     * 在数据数组中进行模糊匹配
     * 
     * @param dataArray 数据数组
     * @param customEventType 自定义事件类型
     * @param adSetId 广告组ID
     * @param dataType 数据类型
     * @return 转化结果
     */
    private ConversionResult fuzzyMatchInDataArray(
            JSONArray dataArray, String customEventType, String adSetId, String dataType) {
        
        ConversionResult result = new ConversionResult();
        result.foundMatch = false;
        
        for (int i = 0; i < dataArray.size(); i++) {
            JSONObject dataItem = dataArray.getJSONObject(i);
            String actionType = dataItem.getString("action_type");
            String value = dataItem.getString("value");
            
            if (StringUtils.isNotBlank(actionType) && StringUtils.isNotBlank(value)) {
                // 模糊匹配：检查actionType是否包含customEventType
                if (actionType.contains(customEventType)) {
                    try {
                        result.conversionValue = new BigDecimal(value);
                        result.conversionKey = customEventType;
                        result.foundMatch = true;
                        log.debug("广告组成效数据-{} 从{}模糊匹配到自定义事件转化成效值：{} (action_type: {}, custom_event: {})", 
                                adSetId, dataType, result.conversionValue, actionType, customEventType);
                        return result;
                    } catch (NumberFormatException e) {
                        log.warn("广告组成效数据-解析{}自定义事件转化成效值失败：{}", dataType, value);
                    }
                }
            }
        }
        
        return result;
    }

    /**
     * 执行数据库操作，带死锁重试机制
     *
     * @param operation     数据库操作
     * @param operationName 操作名称（用于日志）
     * @param startIndex    开始索引
     * @param endIndex      结束索引
     * @return 操作结果
     */
    private <T> T executeWithDeadlockRetry(java.util.function.Supplier<T> operation,
                                           String operationName, int startIndex, int endIndex) {
        int retryCount = 0;

        while (retryCount < MAX_DEADLOCK_RETRIES) {
            try {
                return operation.get();
            } catch (org.springframework.dao.CannotAcquireLockException e) {
                retryCount++;

                // 指数退避延迟
                long delay = BASE_RETRY_DELAY * (1L << (retryCount - 1));
                log.warn("广告组成效数据-{}检测到死锁，范围：{}-{}，第{}次重试，延迟{}ms",
                        operationName, startIndex, endIndex, retryCount, delay);

                try {
                    Thread.sleep(delay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试过程中被中断", ie);
                }
            } catch (org.springframework.dao.DataAccessException e) {
                // 处理其他数据库异常，包括死锁相关异常
                if (e.getMessage() != null && e.getMessage().toLowerCase().contains("deadlock")) {
                    retryCount++;

                    // 指数退避延迟
                    long delay = BASE_RETRY_DELAY * (1L << (retryCount - 1));
                    log.warn("广告组成效数据-{}检测到死锁，范围：{}-{}，第{}次重试，延迟{}ms",
                            operationName, startIndex, endIndex, retryCount, delay);

                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("重试过程中被中断", ie);
                    }
                } else {
                    // 非死锁相关的数据库异常，直接抛出
                    log.error("广告组成效数据-{}失败，范围：{}-{}", operationName, startIndex, endIndex, e);
                    throw e;
                }
            } catch (Exception e) {
                log.error("广告组成效数据-{}失败，范围：{}-{}", operationName, startIndex, endIndex, e);
                throw e;
            }
        }

        log.error("广告组成效数据-{}失败，范围：{}-{}，已达到最大重试次数：{}", operationName, startIndex, endIndex, MAX_DEADLOCK_RETRIES);
        throw new RuntimeException("未知错误：已达到最大重试次数");
    }

}

    
