package top.continew.admin.biz.service;

import com.alibaba.fastjson2.JSONArray;
import top.continew.admin.biz.model.entity.AdAccountTransactionDO;
import top.continew.admin.biz.model.query.AdAccountTransactionQuery;
import top.continew.admin.biz.model.req.AdAccountTransactionReq;
import top.continew.admin.biz.model.resp.AdAccountTransactionDetailResp;
import top.continew.admin.biz.model.resp.AdAccountTransactionResp;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;

/**
 * 广告户交易记录业务接口
 *
 * <AUTHOR>
 * @since 2025/03/10 14:15
 */
public interface AdAccountTransactionService extends BaseService<AdAccountTransactionResp, AdAccountTransactionDetailResp, AdAccountTransactionQuery, AdAccountTransactionReq>, IService<AdAccountTransactionDO> {

    void syncAdAccountBill(String envId, String serialNumber, String proxy, String headers, boolean isLite);

    /**
     * 保存消耗
     *
     * @param platformAdId
     * @param transactions
     */
    void insertAdAccountTransaction(String platformAdId, JSONArray transactions);
}