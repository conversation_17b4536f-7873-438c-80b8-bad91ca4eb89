/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.AdAccountInsightDO;
import top.continew.admin.biz.model.query.AdAccountInsightQuery;
import top.continew.admin.biz.model.req.AdAccountInsightReq;
import top.continew.admin.biz.model.resp.AdAccountInsightDetailResp;
import top.continew.admin.biz.model.resp.AdAccountInsightResp;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 广告户每日消耗业务接口
 *
 * <AUTHOR>
 * @since 2025/01/14 14:43
 */
public interface AdAccountInsightService extends BaseService<AdAccountInsightResp, AdAccountInsightDetailResp, AdAccountInsightQuery, AdAccountInsightReq>, IService<AdAccountInsightDO> {

    void syncAdAccountStatData(String envId,
                               String serialNumber,
                               String proxy,
                               String headers,
                               LocalDate startDate,
                               LocalDate endDate);

    /**
     * 统计指定日期范围内的广告户消耗金额
     *
     * @param platformAdId 广告户ID
     * @param startDate    开始日期
     * @param endDate      结束日期
     * @return 消耗总金额
     */
    BigDecimal sumSpendByDateRange(String platformAdId, String startDate, String endDate);
}