/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service;

import org.springframework.web.multipart.MultipartFile;
import top.continew.admin.biz.model.entity.IpDO;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.IpQuery;
import top.continew.admin.biz.model.req.IpReq;
import top.continew.admin.biz.model.resp.IpDetailResp;
import top.continew.admin.biz.model.resp.IpResp;

import java.util.List;

/**
 * IP库业务接口
 *
 * <AUTHOR>
 * @since 2025/01/20 13:46
 */
public interface IpService extends BaseService<IpResp, IpDetailResp, IpQuery, IpReq>, IService<IpDO> {

    void addUse(Long id);

    void addSuccess(Long id);

    List<IpDO> getAvailableIp(String countryCode);

    void importIp(MultipartFile file, String country,Integer expireDay);


    void addUseCount(Long id, Integer count);

}