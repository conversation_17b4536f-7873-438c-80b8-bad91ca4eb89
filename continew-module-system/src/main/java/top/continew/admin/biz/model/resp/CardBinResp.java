package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.common.base.BaseResp;

/**
 * 卡头管理信息
 *
 * <AUTHOR>
 * @since 2025/09/02 15:44
 */
@Data
@Schema(description = "卡头管理信息")
public class CardBinResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 卡头号码
     */
    @Schema(description = "卡头号码")
    private String cardBin;

    /**
     * 卡头名称
     */
    @Schema(description = "卡头名称")
    private String name;

    /**
     * 所属平台
     */
    @Schema(description = "所属平台")
    private CardPlatformEnum platform;

    /**
     * 卡组织
     */
    @Schema(description = "卡组织")
    private String cardScheme;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    private Boolean enable;

    /**
     * 当前正常状态的开卡数量
     */
    @Schema(description = "当前正常状态的开卡数量")
    private Integer activeCardCount;

    /**
     * 当前冻结状态的开卡数量
     */
    @Schema(description = "当前冻结状态的开卡数量")
    private Integer frozenCardCount;

    /**
     * 总交易金额
     */
    @Schema(description = "总交易金额")
    private BigDecimal totalTransactionAmount;

    /**
     * 近期正常状态的开卡数量
     */
    @Schema(description = "近期正常状态的开卡数量")
    private Integer recentActiveCardCount;

    /**
     * 近期冻结状态的开卡数量
     */
    @Schema(description = "近期冻结状态的开卡数量")
    private Integer recentFrozenCardCount;

    /**
     * 近期交易金额
     */
    @Schema(description = "近期交易金额")
    private BigDecimal recentTransactionAmount;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime creationTime;
}