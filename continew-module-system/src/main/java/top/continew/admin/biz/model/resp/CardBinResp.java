package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.common.base.BaseResp;

/**
 * 卡头管理信息
 *
 * <AUTHOR>
 * @since 2025/09/02 15:44
 */
@Data
@Schema(description = "卡头管理信息")
public class CardBinResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 卡头号码
     */
    @Schema(description = "卡头号码")
    private String cardBin;

    /**
     * 卡头名称
     */
    @Schema(description = "卡头名称")
    private String name;

    /**
     * 所属平台
     */
    @Schema(description = "所属平台")
    private CardPlatformEnum platform;

    /**
     * 卡组织
     */
    @Schema(description = "卡组织")
    private String cardScheme;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    private Boolean enable;

//  开卡数量 总交易金额 近期开卡数量 近期交易金额
    private Integer cardCount;

    private BigDecimal totalAmount;

    private Integer recentCardCount;

    private BigDecimal recentAmount;
}