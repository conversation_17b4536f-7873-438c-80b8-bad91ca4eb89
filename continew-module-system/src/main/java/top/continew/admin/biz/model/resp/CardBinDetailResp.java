package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.common.base.BaseDetailResp;

/**
 * 卡头管理详情信息
 *
 * <AUTHOR>
 * @since 2025/09/02 15:44
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "卡头管理详情信息")
public class CardBinDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 卡头号码
     */
    @Schema(description = "卡头号码")
    @ExcelProperty(value = "卡头号码")
    private String cardBin;

    /**
     * 卡头名称
     */
    @Schema(description = "卡头名称")
    @ExcelProperty(value = "卡头名称")
    private String name;

    /**
     * 所属平台
     */
    @Schema(description = "所属平台")
    @ExcelProperty(value = "所属平台")
    private CardPlatformEnum platform;

    /**
     * 卡组织
     */
    @Schema(description = "卡组织")
    @ExcelProperty(value = "卡组织")
    private String cardScheme;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    @ExcelProperty(value = "是否启用")
    private Boolean enable;
}