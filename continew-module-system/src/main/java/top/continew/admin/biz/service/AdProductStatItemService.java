package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.AdProductStatItemDO;
import top.continew.admin.biz.model.query.AdProductStatItemQuery;
import top.continew.admin.biz.model.req.AdProductStatItemReq;
import top.continew.admin.biz.model.resp.AdProductStatItemDetailResp;
import top.continew.admin.biz.model.resp.AdProductStatItemResp;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;

/**
 * 投放日报明细业务接口
 *
 * <AUTHOR>
 * @since 2025/08/13 21:02
 */
public interface AdProductStatItemService extends BaseService<AdProductStatItemResp, AdProductStatItemDetailResp, AdProductStatItemQuery, AdProductStatItemReq>, IService<AdProductStatItemDO> {}