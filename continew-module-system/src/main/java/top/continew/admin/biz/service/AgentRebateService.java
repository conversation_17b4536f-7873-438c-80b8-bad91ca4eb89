package top.continew.admin.biz.service;

import top.continew.admin.biz.model.entity.AgentRebateDO;
import top.continew.admin.biz.model.query.AgentRebateQuery;
import top.continew.admin.biz.model.req.AgentRebateConfirmReq;
import top.continew.admin.biz.model.req.AgentRebateReq;
import top.continew.admin.biz.model.resp.AgentRebateDetailResp;
import top.continew.admin.biz.model.resp.AgentRebateResp;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.service.BaseService;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 中介返点业务接口
 *
 * <AUTHOR>
 * @since 2025/07/19 11:09
 */
public interface AgentRebateService extends BaseService<AgentRebateResp, AgentRebateDetailResp, AgentRebateQuery, AgentRebateReq>, IService<AgentRebateDO> {

    /**
     * 获取返点总额
     *
     * @param query
     * @return
     */
    Map<String, BigDecimal> getTotalRebateAmount(AgentRebateQuery query);

    void confirmRebate(AgentRebateConfirmReq req);
}