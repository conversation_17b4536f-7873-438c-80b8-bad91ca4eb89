/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service;

import top.continew.admin.biz.model.query.CustomerCycleComparePeriodQuery;
import top.continew.admin.biz.model.query.CustomerCycleCompareSummaryQuery;
import top.continew.admin.biz.model.resp.CustomerCycleComparePeroidResp;
import top.continew.admin.biz.model.resp.CustomerCycleCompareSummaryResp;

import java.util.List;
import java.util.Map;

/**
 * 客户周期对比分析服务
 *
 * <AUTHOR>
 * @since 2024/12/31 10:00
 */
public interface CustomerCycleCompareService {



    /**
     * 分批获取客户的汇总统计数据
     * @param query
     * @return
     */
    List<CustomerCycleCompareSummaryResp> listCustomerSummaryData(CustomerCycleCompareSummaryQuery query);

    /**
     * 分批获取客户的周期统计数据
     * @param query
     * @return
     */
    Map<String, List<CustomerCycleComparePeroidResp>> listCustomerPeriodData(CustomerCycleComparePeriodQuery query);
}