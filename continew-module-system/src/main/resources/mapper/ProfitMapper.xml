<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.ProfitMapper">
    <sql id="base_query_sql">
        select p.*, pt.name as type_name, tu.name as transaction_user_name
        from biz_profit p
                 left join biz_profit_type pt on pt.id = p.type
                 left join biz_transaction_user tu on tu.id = p.transaction_user_id ${ew.customSqlSegment}
    </sql>
    <sql id="stat_by_date_sql">
        SELECT
        DATE(bp.trans_time) AS date,
        SUM(bp.amount) AS amount
        FROM biz_profit bp
        <where>
            <if test="query.type != null">
                and bp.type = #{query.type}
            </if>
            <if test="query.adPlatform != null">
                and bp.ad_platform = #{query.adPlatform}
            </if>
            <if test="query.project != null">
                and bp.project = #{query.project}
            </if>
            <if test="query.action != null">
                <choose>
                    <when test="query.action == 1">
                        and bp.amount > 0
                    </when>
                    <otherwise>
                        and bp.amount &lt; 0
                    </otherwise>
                </choose>
            </if>
            <if test="query.statTimes != null and query.statTimes.length > 0">
                and bp.trans_time between #{query.statTimes[0]} and #{query.statTimes[1]}
            </if>
        </where>
        GROUP BY date
        ORDER BY date DESC
    </sql>
    <sql id="stat_by_type_sql">
        SELECT
        bpt.name AS typeName,
        SUM(bp.amount) AS amount,
        SUM(bp.num) AS num
        FROM biz_profit bp
        LEFT JOIN biz_profit_type bpt ON bpt.id = bp.type
        <where>
            <if test="query.adPlatform != null">
                and bp.ad_platform = #{query.adPlatform}
            </if>
            <if test="query.project != null">
                and bp.project = #{query.project}
            </if>
            <if test="query.action != null">
                <choose>
                    <when test="query.action == 1">
                        and bp.amount > 0
                    </when>
                    <otherwise>
                        and bp.amount &lt; 0
                    </otherwise>
                </choose>
            </if>
            <if test="query.statTimes != null and query.statTimes.length > 0">
                AND bp.trans_time BETWEEN #{query.statTimes[0]} AND #{query.statTimes[1]}
            </if>
        </where>
        GROUP BY bp.type
        ORDER BY ${sort[0]} ${sort[1]}
    </sql>
    <select id="selectCustomPage" resultType="top.continew.admin.biz.model.resp.ProfitResp">
        <include refid="base_query_sql"/>
    </select>
    <select id="selectCustomList" resultType="top.continew.admin.biz.model.resp.ProfitDetailResp">
        <include refid="base_query_sql"/>
    </select>
    <select id="selectStatByDatePage" resultType="top.continew.admin.biz.model.resp.ProfitStatByDateResp">
        <include refid="stat_by_date_sql"></include>
    </select>
    <select id="selectStatByTypeList" resultType="top.continew.admin.biz.model.resp.ProfitStatByTypeResp">
        <include refid="stat_by_type_sql"></include>
    </select>
    <select id="selectStatByDateList" resultType="top.continew.admin.biz.model.resp.ProfitStatByDateResp">
        <include refid="stat_by_date_sql"></include>
    </select>
</mapper>