<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.crm.SalesDailyDataMapper">
    <select id="selectDailyDataPage" resultType="top.continew.admin.biz.model.resp.crm.SalesDailyDataResp">
        select l.*,ur.id as create_user_id,ur.nickname as create_user_string
        from biz_sales_daily_data l
        LEFT JOIN sys_user ur ON l.create_user = ur.id
        <where>
            <if test="query.accountType != null">
                AND l.account_type = #{query.accountType}
            </if>
            <if test="query.createUser != null">
                AND l.create_user = #{query.createUser}
            </if>
            <if test="query.customerName != null">
                AND l.customer_name like CONCAT('%', #{query.customerName}, '%')
            </if>
            <if test="query.customerAccount != null">
                AND l.customer_account like CONCAT('%', #{query.customerAccount}, '%')
            </if>
            <if test="query.recordDate != null and query.recordDate.length == 2">
                AND l.record_date BETWEEN #{query.recordDate[0]} AND #{query.recordDate[1]}
            </if>

            <if test="query.addMethod != null">
                AND l.add_method  = #{query.addMethod}
            </if>
            <if test="query.isLead != null and query.isLead == true">
                AND l.lead_id != 0
            </if>
            <if test="query.isLead != null and query.isLead == false">
                AND l.lead_id = 0
            </if>
        </where>
        ORDER BY l.record_date DESC
    </select>

    <!-- 检查重复数据：根据账号类型、客户账号、创建人、记录日期判断 -->
    <select id="selectDuplicateData" resultType="top.continew.admin.biz.model.entity.crm.SalesDailyDataDO">
        SELECT customer_account
        FROM biz_sales_daily_data
        WHERE account_type = #{accountType}
          AND customer_account = #{customerAccount}
          AND create_user = #{createUser}
    </select>
    <select id="getPreviousDaySalesStatistics"
            resultType="top.continew.admin.biz.model.resp.SalesDailyStatsResp">
        SELECT su.nickname                                                         AS NAME,
               COALESCE(COUNT(bsdd.id), 0)                                         AS totalCount,
               COALESCE(SUM(CASE WHEN bsdd.account_type = 1 THEN 1 ELSE 0 END), 0) AS wechatCount,
               COALESCE(SUM(CASE WHEN bsdd.account_type = 2 THEN 1 ELSE 0 END), 0) AS telegramCount,
               (select count(*)
                from biz_customer
                where business_user_id = su.id
                  AND business_type = 1
                  and create_time >= CURDATE() - INTERVAL 1 DAY
                  AND create_time &lt; CURDATE())                                  as groupCount,
               (select count(*)
                from biz_customer
                where business_user_id = su.id
                  and type = 1
                  and status = 1
                  and cooperate_time >= CURDATE() - INTERVAL 1 DAY
                  AND cooperate_time &lt; CURDATE())                                  as dealCount
        FROM sys_user su
                 LEFT JOIN biz_sales_daily_data bsdd ON su.id = bsdd.create_user
            AND bsdd.record_date = CURDATE() - INTERVAL 1 DAY
        WHERE su.dept_id = 702164640200656226
          AND su.STATUS = 1
          <if test="excludedUserIds != null and excludedUserIds.size() > 0">
              AND su.id NOT IN
              <foreach collection="excludedUserIds" item="userId" open="(" close=")" separator=",">
                  #{userId}
              </foreach>
          </if>
        GROUP BY su.id,
                 su.nickname
    </select>
</mapper>