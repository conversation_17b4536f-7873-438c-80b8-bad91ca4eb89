<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.FinanceStatMapper">
    <select id="selectAdAccountStatPage" resultType="top.continew.admin.biz.model.resp.AdAccountStatResp">
        select a.ad_account_id,b.name as customer_name,a.finish_time,a.recycle_time,c.timezone,c.account_status,
        a.status as
        order_status,
        concat_ws(',', COALESCE(c.browser_id, c.browser_no), c.bm1_browser, bm.ops_browser,
        bm.reserve_browser,bm.reserve_browser_bak,
        bm.observe_browser) as
        browser_no,
        ( SELECT COALESCE(SUM( adt.amount ),0) FROM biz_ad_account_transaction adt WHERE adt.platform_ad_id =
        a.ad_account_id and adt.status != 'failed'

        <choose>
            <when test="query.transTime != null and query.transTime.length > 0">
                AND adt.trans_time <![CDATA[>=]]> GREATEST(#{query.transTime[0]}, a.finish_time)
                AND adt.trans_time <![CDATA[<=]]> LEAST(#{query.transTime[1]}, ifnull(a.recycle_time,
                #{query.transTime[1]}))
            </when>
            <otherwise>
                AND adt.trans_time >= a.finish_time
                AND (a.status != 5 OR adt.trans_time &lt;= a.recycle_time)
            </otherwise>
        </choose>
        ) AS
        adAmountDeductions,
        (
        select COALESCE(SUM(i.spend), 0) from biz_ad_account_insight i
        where i.customer_id=a.customer_id and i.ad_account_id=a.ad_account_id
        <if test="query.transTime != null and query.transTime.length > 0">
            and stat_date<![CDATA[>=]]> date(GREATEST(#{query.transTime[0]}, a.finish_time)) and stat_date<![CDATA[<=]]>
            date(LEAST(#{query.transTime[1]}, ifnull(a.recycle_time,
            #{query.transTime[1]})))
        </if>
        ) as ad_amount,
        (
        select COALESCE(SUM(r.amount), 0) from
        biz_customer_balance_record r
        where r.platform_ad_id=a.ad_account_id and r.customer_id = a.customer_id and r.type=3

        <if test="query.transTime != null and query.transTime.length > 0">
            and r.trans_time<![CDATA[>=]]> #{query.transTime[0]} and r.trans_time<![CDATA[<=]]> #{query.transTime[1]}
        </if>
        ) as recharge,
        (SELECT COALESCE(-SUM(t.trans_amount), 0) as total_amount
        FROM biz_card_transaction t
        WHERE t.ad_account_id = a.ad_account_id AND t.trans_status != 4
        <choose>
            <when test="query.transTime != null and query.transTime.length > 0">
                AND t.stat_time <![CDATA[>=]]> GREATEST(#{query.transTime[0]}, a.finish_time)
                AND t.stat_time <![CDATA[<=]]> LEAST(#{query.transTime[1]}, ifnull(a.recycle_time,
                #{query.transTime[1]}))
            </when>
            <otherwise>
                AND t.stat_time >= a.finish_time
                AND (a.status != 5 OR t.stat_time &lt;= a.recycle_time)
            </otherwise>
        </choose>

        ) as card_amount
        from biz_ad_account_order a
        left join biz_customer b on b.id=a.customer_id
        left join biz_ad_account c on c.platform_ad_id=a.ad_account_id
        left join biz_business_manager bm on bm.id = COALESCE(c.bm1_id, c.business_manager_id)
        where a.status in (3, 5)
        <if test="query.transTime != null and query.transTime.length > 0">
            AND a.finish_time <![CDATA[<=]]> #{query.transTime[1]}
            AND (a.status = 3 or a.recycle_time <![CDATA[>=]]> #{query.transTime[0]})
        </if>
        <if test="query.adAccountIds != null and query.adAccountIds.size() > 0">
            and a.ad_account_id in
            <foreach collection="query.adAccountIds" item="adAccountId" open="(" separator="," close=")">
                #{adAccountId}
            </foreach>
        </if>
        <if test="query.customerId != null">
            and a.customer_id=#{query.customerId}
        </if>
        <if test="query.orderStatus != null">
            and a.status=#{query.orderStatus}
        </if>
        <if test="query.accountStatus != null">
            and c.account_status=#{query.accountStatus}
        </if>
        <choose>
            <when test="query.adAccountIds != null and query.adAccountIds.size() > 0">
                ORDER BY FIELD(a.ad_account_id,
                <foreach collection="query.adAccountIds" item="adAccountId" separator=",">
                    #{adAccountId}
                </foreach>
                )
            </when>
            <when test="null != query.sortField and query.sortField != ''">
                ORDER BY ${query.sortField}
                <choose>
                    <when test="null != query.ascSortFlag and query.ascSortFlag">
                        ASC
                    </when>
                    <otherwise>
                        DESC
                    </otherwise>
                </choose>
            </when>
        </choose>
    </select>

    <select id="selectAdAccountDailyStatReport"
            resultType="top.continew.admin.biz.model.resp.AdAccountDailyStatReportResp">
        SELECT
        statDate,
        SUM( spend ) AS spend,
        SUM( recharge ) AS recharge
        FROM
        (
        SELECT
        a.stat_date AS statDate,
        a.spend,
        0 AS recharge
        FROM
        biz_ad_account_insight a
        WHERE
        a.ad_account_id = #{adAccountId}
        <if test="start != null and end != null">
            and a.stat_date between #{start} and #{end}
        </if>
        UNION ALL
        SELECT
        DATE( trans_time ) AS statDate,
        0 AS spend,
        SUM( amount ) AS recharge
        FROM
        biz_ad_account_balance_record
        WHERE
        platform_ad_id = #{adAccountId}
        AND type = 1
        <if test="start != null and end != null">
            and trans_time between #{start} and #{end}
        </if>
        GROUP BY
        DATE( trans_time )
        ) AS combined
        GROUP BY
        statDate
        ORDER BY
        statDate DESC
    </select>

    <sql id="daily_stat_report_sql">
        SELECT
        stat_date,
        COALESCE(SUM(card_spent), 0) AS card_spent,
        COALESCE(SUM(transfer_amount), 0) AS transfer_amount,
        COALESCE(SUM(refund_amount), 0) AS refund_amount,
        COALESCE(SUM(fee), 0) AS fee,
        COALESCE(SUM(total_recharge), 0) AS total_recharge,
        COALESCE(SUM(ad_account_buy_amount), 0) AS ad_account_buy_amount
        FROM (
        SELECT
        DATE(t.stat_time) AS stat_date,
        -SUM(t.trans_amount) AS card_spent,
        NULL AS transfer_amount,
        NULL AS refund_amount,
        NULL AS fee,
        NULL AS total_recharge,
        NULL AS ad_account_buy_amount
        FROM
        biz_card_transaction t
        <if test="customerIndustry != null">
            left join biz_customer c on t.customer_id = c.id
        </if>
        WHERE
        t.trans_status != 4
        <if test="customerId != null">
            <if test="customerId == -1">
                AND t.customer_id is not null
                AND t.customer_id not in (select id from biz_customer where is_self_account = true or business_type = 2)
            </if>
            <if test="customerId == -2">
                AND t.ad_account_id != '' AND t.customer_id is null
            </if>
            <if test="customerId == -3">
                AND t.customer_id is not null
                AND t.customer_id in (select id from biz_customer where business_type = 2)
            </if>
            <if test="customerId > 0">
                AND t.customer_id = #{customerId}
            </if>
        </if>
        <if test="customerIndustry != null">
            AND c.industry = #{customerIndustry}
        </if>
        <if test="start != null and end != null">
            AND t.stat_time between #{start} and #{end}
        </if>
        GROUP BY
        stat_date

        UNION ALL

        SELECT
        DATE(t.trans_time) AS stat_date,
        NULL AS card_spent,
        SUM(IF(t.type = 1, t.amount, 0)) AS transfer_amount,
        SUM(IF(t.type = 8, t.amount, 0)) AS refund_amount,
        SUM(IF(t.type = 2, t.amount, 0)) AS fee,
        SUM(IF(t.type in (1, 9), t.amount, 0)) - SUM(IF(t.type IN (2, 6), t.amount, 0)) AS total_recharge,
        SUM(IF(t.type = 6, t.amount, 0)) - SUM(IF(t.type = 9, t.amount, 0)) AS ad_account_buy_amount
        FROM
        biz_customer_balance_record t
        <if test="customerIndustry != null">
            left join biz_customer c on t.customer_id = c.id
        </if>
        <where>
            <if test="start != null and end != null">
                AND t.trans_time BETWEEN #{start} AND #{end}
            </if>
            <if test="customerId != null">
                <if test="customerId == -1">
                    AND t.customer_id is not null
                    AND t.customer_id not in (select id from biz_customer where is_self_account = true or business_type = 2)
                </if>
                <if test="customerId == -2">
                    AND t.customer_id = -1
                </if>
                <if test="customerId == -3">
                    AND t.customer_id is not null
                    AND t.customer_id in (select id from biz_customer where business_type = 2)
                </if>
                <if test="customerId > 0">
                    AND t.customer_id = #{customerId}
                </if>
            </if>
            <if test="customerIndustry != null">
                AND c.industry = #{customerIndustry}
            </if>
        </where>
        GROUP BY
        stat_date
        ) AS combined
        GROUP BY stat_date
        ORDER BY stat_date desc;
    </sql>

    <select id="selectDailyStatReportPage" resultType="top.continew.admin.biz.model.resp.DailyStatReportResp">
        <include refid="daily_stat_report_sql"/>
    </select>
    <select id="selectDailyStatReportList" resultType="top.continew.admin.biz.model.resp.DailyStatReportResp">
        <include refid="daily_stat_report_sql"/>
    </select>

    <select id="selectAdAccountRetentionPage" resultType="top.continew.admin.biz.model.resp.AdAccountRetentionResp">
        SELECT
        DATE(a.finish_time) as date,
        IFNULL(SUM(a.cost), 0) as cost,
        COUNT(DISTINCT b.id) as count,
        COUNT(DISTINCT CASE WHEN b.ban_time IS NOT NULL
        AND b.account_status = 2
        AND DATE(b.ban_time) <![CDATA[<=]]> DATE_ADD(DATE(a.finish_time), INTERVAL 1 DAY)
        THEN b.id END) as ban_count_1d,
        COUNT(DISTINCT CASE WHEN b.ban_time IS NOT NULL
        AND b.account_status = 2
        AND DATE(b.ban_time) <![CDATA[<=]]> DATE_ADD(DATE(a.finish_time), INTERVAL 7 DAY)
        THEN b.id END) as ban_count_7d,
        COUNT(DISTINCT CASE WHEN b.ban_time IS NOT NULL
        AND b.account_status = 2
        AND DATE(b.ban_time) <![CDATA[<=]]> DATE_ADD(DATE(a.finish_time), INTERVAL 15 DAY)
        THEN b.id END) as ban_count_15d,
        COUNT(DISTINCT CASE WHEN b.ban_time IS NOT NULL
        AND b.account_status = 2
        AND DATE(b.ban_time) <![CDATA[<=]]> DATE_ADD(DATE(a.finish_time), INTERVAL 30 DAY)
        THEN b.id END) as ban_count_30d
        FROM biz_ad_account_order a
        INNER JOIN biz_ad_account b ON a.ad_account_id = b.platform_ad_id
        LEFT JOIN biz_business_manager c ON b.business_manager_id = c.id
        WHERE a.status=3
        <if test="query.customerId != null">
            AND a.customer_id = #{query.customerId}
        </if>
        <if test="query.bmChannel != null">
            AND c.channel_id = #{query.bmChannel}
        </if>
        <if test="query.timezone != null and query.timezone.length!=0">
            AND b.timezone = #{query.timezone}
        </if>
        <if test="query.finishTime != null and query.finishTime.length > 0">
            AND a.finish_time BETWEEN #{query.finishTime[0]} AND #{query.finishTime[1]}
        </if>
        GROUP BY DATE(a.finish_time)
        ORDER BY date DESC
    </select>


</mapper>