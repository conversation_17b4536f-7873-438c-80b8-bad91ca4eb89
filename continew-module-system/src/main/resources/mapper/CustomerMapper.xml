<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.CustomerMapper">
    <sql id="baseQuerySql">
        WITH customer_card_spent as (
        SELECT customer_id, COALESCE(-SUM(trans_amount), 0) as card_spent
        FROM biz_card_transaction
        WHERE trans_status != 4 and customer_id is not null
        <if test="start != null and end != null">
            AND stat_time <![CDATA[>=]]> #{start}
            AND stat_time <![CDATA[<=]]> #{end}
        </if>
        group by customer_id
        ),customer_fb_balance as (
        select temp.customer_id,
        SUM(IF(!enable_prepay and order_method = 1, (spend_cap - amount_spent), 0)) +
        SUM(IF(enable_prepay, (recharge_amount - total_spent), 0)) as fb_balance
        from (SELECT o.customer_id,
        o.enable_prepay,
        o.total_spent,
        ad.spend_cap,
        ad.amount_spent,
        o.order_method,
        (select COALESCE(sum(IF(r.type =
        3, r.amount, 0)), 0) -
        COALESCE(sum(IF(r.type in (4, 5), r.amount, 0)), 0)
        from biz_customer_balance_record r
        where r.platform_ad_id = o.ad_account_id
        and r.customer_id = o.customer_id
        AND r.trans_time >= o.pay_time) as recharge_amount
        from biz_ad_account_order o
        left join
        biz_ad_account ad on ad.platform_ad_id =
        o.ad_account_id
        where o.status = 3
        and o.clear_status != 3) temp
        group by temp.customer_id
        ), customer_balance AS (
        SELECT
        customer_id,
        SUM(IF(type = 1, amount, 0)) AS transferAmount,
        SUM(IF(type = 8, amount, 0)) AS refundAmount,
        IFNULL(SUM(IF(type = 2, amount, 0)) - SUM(IF(type = 10, amount, 0)), 0) AS fee,
        SUM(IF(type = 3, amount, 0)) AS adAccountRechargeAmount,
        SUM(IF(type IN (4, 5), amount, 0)) AS withdrawAmount,
        IFNULL(SUM(IF(type in (1, 9), amount, 0)) - SUM(IF(type IN (2, 6), amount, 0)), 0) AS totalRecharge,
        IFNULL(SUM(CASE WHEN type = 6 THEN amount END), 0) - IFNULL(SUM(CASE WHEN type = 9 THEN amount END),
        0) AS adAccountBuyAmount
        FROM
        biz_customer_balance_record
        <where>
            <if test="start != null and end != null">
                trans_time BETWEEN #{start} AND #{end}
            </if>
        </where>
        GROUP BY
        customer_id
        )

        SELECT c.id AS customer_id,
        c.name AS customer_name,
        c.balance,
        COALESCE(bs.transferAmount, 0) AS transferAmount,
        COALESCE(bs.refundAmount, 0) AS refundAmount,
        COALESCE(bs.fee, 0) AS fee,
        COALESCE(bs.adAccountRechargeAmount, 0) AS adAccountRechargeAmount,
        COALESCE(bs.withdrawAmount, 0) AS withdrawAmount,
        COALESCE(bs.totalRecharge, 0) AS totalRecharge,
        COALESCE(bs.adAccountBuyAmount, 0) AS AdAccountBuyAmount,
        COALESCE(cs.card_spent, 0) as cardSpent,
        COALESCE(fb.fb_balance, 0) as fbBalance,
        COALESCE(fb.fb_balance, 0) + c.balance as lastBalance
        FROM biz_customer c
        LEFT JOIN
        customer_balance bs ON c.id = bs.customer_id
        LEFT JOIN customer_card_spent cs ON c.id = cs.customer_id
        LEFT JOIN customer_fb_balance fb on c.id = fb.customer_id
        <where>
            c.type = 1
            <if test="customerId != null">
                and c.id = #{customerId}
            </if>
            <if test="isSelf != null">
                and c.is_self_account = #{isSelf}
            </if>
            <if test="settleType != null">
                and c.settle_type = #{settleType}
            </if>
            <if test="businessType != null">
                and c.business_type = #{businessType}
            </if>
            <if test="customerIndustry != null">
                and c.industry = #{customerIndustry}
            </if>
        </where>
    </sql>
    <sql id="dailyStatReportSql">
        SELECT
        stat_date,
        COALESCE(SUM(card_spent), 0) AS card_spent,
        COALESCE(SUM(transfer_amount), 0) AS transfer_amount,
        COALESCE(SUM(refund_amount), 0) AS refund_amount,
        COALESCE(SUM(fee), 0) AS fee,
        COALESCE(SUM(ad_account_recharge_amount), 0) AS ad_account_recharge_amount,
        COALESCE(SUM(withdraw_amount), 0) AS withdraw_amount,
        COALESCE(SUM(total_recharge), 0) AS total_recharge,
        COALESCE(SUM(ad_account_buy_amount), 0) AS ad_account_buy_amount
        FROM (
        SELECT
        DATE(t.stat_time) AS stat_date,
        -SUM(t.trans_amount) AS card_spent,
        NULL AS transfer_amount,
        NULL AS refund_amount,
        NULL AS fee,
        NULL AS ad_account_recharge_amount,
        NULL AS withdraw_amount,
        NULL AS total_recharge,
        NULL AS ad_account_buy_amount
        FROM
        biz_card_transaction t
        INNER JOIN
        biz_ad_account_order o ON o.ad_account_id = t.ad_account_id and o.status in (3, 5)
        <if test="customerId != null">
            and o.customer_id = #{customerId}
        </if>
        WHERE
        t.trans_status != 4
        <choose>
            <when test="start != null and end != null">
                AND t.stat_time <![CDATA[>=]]> GREATEST(#{start}, o.finish_time)
                AND t.stat_time <![CDATA[<=]]> LEAST(#{end}, ifnull(o.recycle_time,
                #{end}))
            </when>
            <otherwise>
                AND t.stat_time >= o.finish_time
                AND (o.status != 5 OR t.stat_time &lt;=
                o.recycle_time)
            </otherwise>
        </choose>
        GROUP BY
        stat_date

        UNION ALL

        SELECT
        DATE(trans_time) AS stat_date,
        NULL AS card_spent,
        SUM(IF(type = 1, amount, 0)) AS transfer_amount,
        SUM(IF(type = 8, amount, 0)) AS refund_amount,
        IFNULL(SUM(IF(type = 2, amount, 0)) - SUM(IF(type = 10, amount, 0)), 0) AS fee,
        SUM(IF(type = 3, amount, 0)) AS ad_account_recharge_amount,
        SUM(IF(type IN (4, 5), amount, 0)) AS withdraw_amount,
        SUM(IF(type in (1, 9), amount, 0)) - SUM(IF(type IN (2, 6), amount, 0)) AS total_recharge,
        SUM(IF(type = 6, amount, 0)) - SUM(IF(type = 9, amount, 0)) AS ad_account_buy_amount
        FROM
        biz_customer_balance_record
        <where>
            <if test="customerId != null">
                customer_id = #{customerId}
            </if>
            <if test="start != null and end != null">
                AND trans_time BETWEEN #{start} AND #{end}
            </if>
        </where>
        GROUP BY
        stat_date
        ) AS combined
        GROUP BY stat_date
        ORDER BY stat_date desc;
    </sql>

    <select id="selectCustomerStatReport"
            resultType="top.continew.admin.biz.model.resp.CustomerStatReportResp">

        <include refid="baseQuerySql"/>

        <if test="null != sortField and sortField.length!=0">
            ORDER BY ${sortField}
            <choose>
                <when test="null != ascSortFlag and ascSortFlag">
                    ASC
                </when>
                <otherwise>
                    DESC
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="listCustomerStatReport" resultType="top.continew.admin.biz.model.resp.CustomerStatReportResp">
        <include refid="baseQuerySql"/>
    </select>

    <select id="selectCustomerDailyStatReport"
            resultType="top.continew.admin.biz.model.resp.CustomerDailyStatReportResp">
        <include refid="dailyStatReportSql"/>
    </select>

    <select id="listCustomerDailyStatReport"
            resultType="top.continew.admin.biz.model.resp.CustomerDailyStatReportResp">
        <include refid="dailyStatReportSql"/>
    </select>
    <select id="getCustomerStatReportSummary"
            resultType="top.continew.admin.biz.model.resp.CustomerStatSummaryResp">
        SELECT
        SUM(IF(bc.type = 1, bc.amount, 0)) AS transfer_amount,
        SUM(IF(bc.type = 8, bc.amount, 0)) AS refund_amount,
        IFNULL(SUM(IF(bc.type = 2, bc.amount, 0)) - SUM(IF(bc.type = 10, bc.amount, 0)), 0) AS fee,
        IFNULL(SUM(CASE WHEN bc.type = 3 THEN bc.amount END), 0) AS ad_account_recharge_amount,
        IFNULL(SUM(CASE WHEN bc.type IN (4, 5) THEN bc.amount END), 0) AS withdraw_amount,
        IFNULL(SUM(IF(bc.type in (1, 9), bc.amount, 0)) - SUM(IF(bc.type IN (2, 6), bc.amount, 0)), 0) AS
        total_recharge,
        IFNULL(SUM(CASE WHEN bc.type = 6 THEN bc.amount END), 0) - IFNULL(SUM(CASE WHEN bc.type = 9 THEN bc.amount END),
        0) AS ad_account_buy_amount
        FROM
        biz_customer_balance_record bc left join biz_customer c on c.id = bc.customer_id
        <where>
            <if test="query.customerId != null">
                AND bc.customer_id = #{query.customerId}
            </if>
            <if test="query.isSelf != null">
                and c.is_self_account = #{query.isSelf}
            </if>
            <if test="query.settleType != null">
                and c.settle_type = #{query.settleType}
            </if>
            <if test="query.businessType != null">
                and c.business_type = #{query.businessType}
            </if>
            <if test="query.customerIndustry != null">
                and c.industry = #{query.customerIndustry}
            </if>
            <if test="query.transTime != null and query.transTime.length > 0">
                AND bc.trans_time BETWEEN #{query.transTime[0]} AND #{query.transTime[1]}
            </if>
        </where>
    </select>
    <select id="selectCustomerOrderStatisticsPage"
            resultType="top.continew.admin.biz.model.resp.CustomerOrderStatisticsResp">
        SELECT
        baa.timezone,
        COUNT(*) AS saleCount
        FROM
        biz_ad_account_order bao
        INNER JOIN
        biz_ad_account baa
        ON bao.ad_account_id = baa.platform_ad_id
        <where>
            bao.status = 3
            <if test=" query.startTime!=null and query.endTime!=null">
                and bao.finish_time between #{query.startTime} and #{query.endTime}
            </if>
        </where>
        GROUP BY
        baa.timezone
        ORDER BY
        saleCount DESC;
    </select>


    <select id="pageCustomers" resultType="top.continew.admin.biz.model.entity.CustomerDO">
        SELECT DISTINCT c.*
        FROM biz_customer c
        <if test="query.tagId != null">
            INNER JOIN biz_customer_tag ct ON c.id = ct.customer_id
            AND ct.tag_id = #{query.tagId}
        </if>
        <where>
            <if test="query.name != null and query.name != ''">
                AND c.name LIKE CONCAT('%', #{query.name}, '%')
            </if>
            <if test="query.businessUserId != null">
                AND c.business_user_id = #{query.businessUserId}
            </if>
            <if test="query.status != null">
                AND c.status = #{query.status}
            </if>
            <if test="query.type !=null">
                AND c.type=#{query.type}
            </if>
            <if test="query.sourceId!=null">
                AND c.source_id=#{query.sourceId}
            </if>
            <if test="query.industry!=null">
                AND c.industry=#{query.industry}
            </if>
            <if test="query.city!=null">
                AND c.city LIKE CONCAT('%', #{query.city}, '%')
            </if>
            <if test="query.customerIds != null and query.customerIds.size()>0">
                AND c.id IN
                <foreach collection="query.customerIds" item="customerId" open="(" separator="," close=")">
                    #{customerId}
                </foreach>
            </if>

            <if test="query.isSelfAccount != null">
                AND c.is_self_account = #{query.isSelfAccount}
            </if>

            <if test="query.businessType != null">
                AND c.business_type = #{query.businessType}
            </if>

            <if test="query.cooperateTime != null and query.cooperateTime.length > 0">
                AND c.cooperate_time between  #{query.cooperateTime[0]} and #{query.cooperateTime[1]}
            </if>
            <if test="query.lastCooperateTime != null and query.lastCooperateTime.length > 0">
                AND c.last_cooperate_time between  #{query.lastCooperateTime[0]} and #{query.lastCooperateTime[1]}
            </if>
            <if test="query.createTime != null and query.createTime.length > 0">
                AND c.create_time between  #{query.createTime[0]} and #{query.createTime[1]}
            </if>
            <if test="query.customerCategory != null and query.customerCategory != ''">
                <if test="query.customerCategory == 'new'">
                    AND c.create_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                </if>
                <if test="query.customerCategory == 'old'">
                    AND c.create_time &lt; DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                </if>
            </if>

        </where>
        ORDER BY c.id DESC
    </select>
    <sql id="select_customer_profit_stat_sql">
        with customer_profit as (SELECT customer_id,
                                        IFNULL(SUM(IF(type = 2, amount, 0)), 0)                     AS service_fee,
                                        IFNULL(SUM(IF(type = 10, amount, 0)), 0)                    AS refund_service_fee,
                                        SUM(IF(type = 6, amount, 0)) - SUM(IF(type = 9, amount, 0)) AS open_account_fee
                                 FROM biz_customer_balance_record
                                 group by customer_id),

             ad_account_cost as (select customer_id,
                                        COUNT(*)                                                   as total_account,
                                        COUNT(IF(status = 5 or cost_party = 1, 1, null))           as self_cost_account,
                                        IFNULL(SUM(IF(status = 5 or cost_party = 1, cost, 0)), 0)  as self_cost,
                                        IFNULL(SUM(IF(status = 3 and cost_party = 2, cost, 0)), 0) as customer_cost
                                 from biz_ad_account_order
                                 where status in (3, 5)
                                 group by customer_id),

             agent_rebate as (select customer_id, SUM(actual_rebate_amount) as rebate_amount
                              from biz_agent_rebate
                              group by customer_id),
             customer_refund_date as (select customer_id, max(actual_refund_time) as refund_time
                                      from biz_customer_withdraw_order
                                      where status = 4
                                      group by customer_id)
        select c.id,
               c.name,
               DATE(c.create_time)                                                          as create_time,
               DATE(crd.refund_time)                                                        as refund_time,
               DATEDIFF(IFNULL(DATE(crd.refund_time), CURRENT_DATE()), DATE(c.create_time)) as cooperate_day,
               CONCAT(c.fee_rate_percent, '%', '+', FORMAT(c.buy_account_fee, 0))           as cooperate_policy,
               p.service_fee,
               p.refund_service_fee,
               p.open_account_fee,
               aac.total_account,
               aac.self_cost,
               aac.self_cost_account,
               aac.customer_cost,
               (aac.self_cost + aac.customer_cost)                                          as total_cost,
               COALESCE(ar.rebate_amount, 0)                                                as rebate_amount,
               (p.service_fee + p.open_account_fee - p.refund_service_fee - aac.self_cost - aac.customer_cost -
                COALESCE(ar.rebate_amount, 0))                                              as profit
        from biz_customer c
                 left join customer_profit p on p.customer_id = c.id
                 left join ad_account_cost aac on aac.customer_id = c.id
                 left join agent_rebate ar on ar.customer_id = c.id
                 left join customer_refund_date crd on crd.customer_id = c.id
            ${ew.customSqlSegment}
    </sql>
    <select id="selectCustomerProfitStatPage"
            resultType="top.continew.admin.biz.model.resp.CustomerProfitStatResp">
        <include refid="select_customer_profit_stat_sql"/>
    </select>
    <select id="selectCustomerProfitStatList"
            resultType="top.continew.admin.biz.model.resp.CustomerProfitStatResp">
        <include refid="select_customer_profit_stat_sql"/>
    </select>
    <select id="selectCustomerNameByAdAccountId" resultType="java.lang.String">
        select c.name
        from biz_customer c
                 left join biz_ad_account_order o on c.id = o.customer_id
        where o.ad_account_id = #{adAccountId}
          and o.status = 3
        order by o.id desc
        limit 1
    </select>
    <sql id="selectToufangCustomerSql">
        WITH customer_country as (select customer_id, GROUP_CONCAT(distinct country) as country
        from biz_ad_product
        group by customer_id),
        customer_card_spent as (
        SELECT customer_id, COALESCE(-SUM(trans_amount), 0) as card_spent
        FROM biz_card_transaction
        WHERE trans_status != 4 and customer_id is not null
        <if test="start != null and end != null">
            AND stat_time <![CDATA[>=]]> #{start}
            AND stat_time <![CDATA[<=]]> #{end}
        </if>
        group by customer_id),
        customer_balance AS (
        SELECT
        customer_id,
        SUM(IF(type = 1, amount, 0)) AS transferAmount,
        SUM(IF(type = 8, amount, 0)) AS refundAmount,
        SUM(IF(type = 3, amount, 0)) AS adAccountRechargeAmount,
        SUM(IF(type IN (4, 5), amount, 0)) AS withdrawAmount
        FROM
        biz_customer_balance_record
        <where>
            <if test="start != null and end != null">
                trans_time BETWEEN #{start} AND #{end}
            </if>
        </where>
        GROUP BY
        customer_id
        ),
        customer_product_data as (select customer_id,
        (COALESCE(SUM(spend), 0) + COALESCE(SUM(reflow_spend), 0)) as spend,
        COALESCE(SUM(fee), 0) as fee,
        (COALESCE(SUM(spend), 0) + COALESCE(SUM(fee), 0) + COALESCE(SUM(reflow_spend), 0)) as total_spend
        from biz_ad_product_stat
        <where>
            <if test="start != null and end != null">
                stat_date BETWEEN DATE(#{start}) AND DATE(#{end})
            </if>
        </where>
        group by customer_id)

        select c.id,
        c.name,
        c.status,
        c.create_time,
        c.balance,
        c.fee_rate_percent as fee_rate,
        cc.country as country,
        COALESCE(cb.transferAmount, 0) as total_transfer,
        COALESCE(cb.refundAmount, 0) as refund_amount,
        COALESCE(cb.adAccountRechargeAmount, 0) as ad_account_recharge_amount,
        COALESCE(cb.withdrawAmount, 0) as withdraw_amount,
        COALESCE(cpd.spend, 0) as spend,
        COALESCE(cpd.fee, 0) as fee,
        COALESCE(cpd.total_spend, 0) as total_spend,
        COALESCE(cb.transferAmount, 0) - COALESCE(cpd.total_spend, 0) as toufang_balance,
        COALESCE(ccs.card_spent, 0) as card_spent
        from biz_customer c
        left join customer_country cc on cc.customer_id = c.id
        left join customer_balance cb on cb.customer_id = c.id
        left join customer_card_spent ccs on ccs.customer_id = c.id
        left join customer_product_data cpd on cpd.customer_id = c.id ${ew.customSqlSegment}
    </sql>
    <select id="selectToufangCustomerPage" resultType="top.continew.admin.biz.model.resp.ToufangCustomerResp">
        <include refid="selectToufangCustomerSql" />
    </select>
    <select id="selectToufangCustomerList" resultType="top.continew.admin.biz.model.resp.ToufangCustomerResp">
        <include refid="selectToufangCustomerSql" />
    </select>
</mapper>