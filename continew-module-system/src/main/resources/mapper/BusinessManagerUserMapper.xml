<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.BusinessManagerUserMapper">
    <select id="selectCustomPage" resultType="top.continew.admin.biz.model.resp.BusinessManagerUserResp">
        select u.*,
               concat_ws(',', '', '', bm.ops_browser, bm.reserve_browser,
                         bm.observe_browser)                                            as bm_browser,
               bm.create_time                                                           as bm_create_time,
               bmc.name                                                                 as channel_name,
               (exists(select 1 from biz_white_email pa where pa.email = u.user_email)) as is_self_user,
               pt.name                                                                  as type_name
        from biz_business_manager_user u
                 left join biz_business_manager bm on bm.platform_id = u.bm_id
                 LEFT JOIN biz_profit_type pt on pt.id = bm.type
                 left join biz_business_manager_channel bmc on bmc.id = bm.channel_id ${ew.customSqlSegment}
    </select>
    <select id="selectCustomList" resultType="top.continew.admin.biz.model.resp.BusinessManagerUserDetailResp">
        select u.*,
               concat_ws(',', '', '', bm.ops_browser, bm.reserve_browser,
                         bm.observe_browser)                                            as bm_browser,
               bm.create_time                                                           as bm_create_time,
               bmc.name                                                                 as channel_name,
               (exists(select 1 from biz_white_email pa where pa.email = u.user_email)) as is_self_user,
               pt.name                                                                  as type_name
        from biz_business_manager_user u
                 left join biz_business_manager bm on bm.platform_id = u.bm_id
                 LEFT JOIN biz_profit_type pt on pt.id = bm.type
                 left join biz_business_manager_channel bmc on bmc.id = bm.channel_id ${ew.customSqlSegment}
    </select>
</mapper>