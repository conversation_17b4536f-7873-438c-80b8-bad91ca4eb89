package top.continew.admin.controller.biz;

import com.alibaba.fastjson2.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.biz.katai.strategy.impl.ItlOpsStrategyImpl;
import top.continew.admin.biz.model.entity.CardDO;
import top.continew.admin.biz.service.CardService;
import top.continew.admin.biz.service.CardTransactionService;
import top.continew.katai.GzyClient;
import top.continew.starter.cache.redisson.util.RedisUtils;
import top.continew.starter.log.annotation.Log;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.BufferedReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/13 16:47
 */
@Log(ignore = true)
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
@Slf4j
public class ApiItlWebhookController {
    private final CardTransactionService cardTransactionService;
    private final CardService cardService;

    // 幂等处理，生产建议用 Redis 或数据库
    private final Set<String> processedIds = ConcurrentHashMap.newKeySet();

    // 替换为你的 CLIENT_SECRET
    private static final String CLIENT_SECRET = "c146507d01b140eea50383ccb68f964e";

    @PostMapping("/itl/webhook")
    public void handleWebhook(@RequestBody JSONObject payload, HttpServletResponse response) throws IOException {

        String id = payload.getString("id");
        String businessType = payload.getString("businessType");
        JSONObject data = payload.getJSONObject("data");
        String sign = payload.getString("sign");


        // 签名校验
//        log.info("itl businessType:{}, data:{}", businessType, JSON.toJSONString(data));
//        if (!verifySignature(data, sign)) {
//            log.warn("itl 签名校验失败，businessType:{}, data:{}, sign:{}", businessType, JSON.toJSONString(data), sign);
//        }

        // 业务处理
        switch (businessType) {
            case "Card3dsOtp":
                handleCard3dsOtp(data);
                break;
            case "CardTransaction":
                handleCardTransaction(data);
                break;
            default:
                // 只处理指定类型
               break;
        }

        response.setStatus(HttpServletResponse.SC_OK);
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write("{\"received\":true}");
    }

    private void handleCard3dsOtp(JSONObject data) {
        // 处理卡片3DS OTP
       try {
           // 提取所需字段
           String cardId = data.getString("cardId");
           String otp = data.getString("otp");
           String cardNumber = data.getString("cardNumber");

           // 创建包含所需信息的JSONObject
           JSONObject otpInfo = new JSONObject();
           otpInfo.put("cardId", cardId);
           otpInfo.put("otp", otp);
           otpInfo.put("cardNumber", cardNumber);
           otpInfo.put("timestamp", System.currentTimeMillis());

           // 存储到Redis，限制只存储最近200条记录
           // 获取现有列表
           List<JSONObject> existingList = RedisUtils.getList(ItlOpsStrategyImpl.ITL_3DS_CODE_KEY);
           if (existingList == null) {
               existingList = new ArrayList<>();
           }

           // 添加新记录到列表开头
           existingList.add(0, otpInfo);

           // 限制列表大小为200
           if (existingList.size() > 200) {
               existingList = existingList.subList(0, 200);
           }

           // 保存更新后的列表
           RedisUtils.setList(ItlOpsStrategyImpl.ITL_3DS_CODE_KEY, existingList);
       } catch (Exception e) {
           log.error("处理卡片3DS OTP失败", e);
       }
    }

    private void handleCardTransaction(JSONObject data) {
        // 处理卡片交易流水
        try {
            cardTransactionService.handleItlTransaction(JSONArray.of(data));
        } catch (Exception e) {
            log.error("交易通知处理失败", e);
        }
    }


    /**
     * 签名校验
     */
    private boolean verifySignature(JSONObject data, String sign) {
        try {
            // fastjson2 JSONObject 转 Map<String, Object>
            Map<String, Object> dataMap = data == null
                    ? new HashMap<>()
                    : JSON.parseObject(data.toJSONString(), new TypeReference<Map<String, Object>>() {});
            String joinStr = joinStr(dataMap);
            String calcSign = hmacSha256(joinStr, CLIENT_SECRET);
            return calcSign.equalsIgnoreCase(sign);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 按照文档规则拼接字符串
     */
    private String joinStr(Map<String, Object> data) {
        List<String> keys = new ArrayList<>(data.keySet());
        Collections.sort(keys);
        StringBuilder sb = new StringBuilder();
        for (String key : keys) {
            Object val = data.get(key);
            if (val == null) {
                val = "";
            }
            if (val instanceof Map || val instanceof List) {
                val = JSON.toJSONString(val);
            }
            sb.append(key).append("=").append(val).append("&");
        }
        if (sb.length() > 0) {
            sb.setLength(sb.length() - 1); // 去掉最后一个 &
        }
        return sb.toString();
    }

    /**
     * HMAC-SHA256 签名
     */
    private String hmacSha256(String message, String secret) throws Exception {
        Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
        SecretKeySpec secret_key = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        sha256_HMAC.init(secret_key);
        byte[] hash = sha256_HMAC.doFinal(message.getBytes(StandardCharsets.UTF_8));
        StringBuilder hexString = new StringBuilder(2 * hash.length);
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) hexString.append('0');
            hexString.append(hex);
        }
        return hexString.toString();
    }



    private static boolean verifySignature1(JSONObject data, String sign) {
        try {
            // fastjson2 JSONObject 转 Map<String, Object>
            Map<String, Object> dataMap = data == null
                    ? new HashMap<>()
                    : JSON.parseObject(data.toJSONString(), new TypeReference<Map<String, Object>>() {});
            String joinStr = joinStr1(dataMap);
            String calcSign = hmacSha2561(joinStr, CLIENT_SECRET);
            return calcSign.equalsIgnoreCase(sign);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 按照文档规则拼接字符串
     */
    private static String joinStr1(Map<String, Object> data) {
        List<String> keys = new ArrayList<>(data.keySet());
        Collections.sort(keys);
        StringBuilder sb = new StringBuilder();
        for (String key : keys) {
            Object val = data.get(key);
            if (val == null) {
                val = "";
            }
            if (val instanceof Map || val instanceof List) {
                val = JSON.toJSONString(val);
            }
            sb.append(key).append("=").append(val).append("&");
        }
        if (sb.length() > 0) {
            sb.setLength(sb.length() - 1); // 去掉最后一个 &
        }
        return sb.toString();
    }

    /**
     * HMAC-SHA256 签名
     */
    private static String hmacSha2561(String message, String secret) throws Exception {
        Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
        SecretKeySpec secret_key = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        sha256_HMAC.init(secret_key);
        byte[] hash = sha256_HMAC.doFinal(message.getBytes(StandardCharsets.UTF_8));
        StringBuilder hexString = new StringBuilder(2 * hash.length);
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) hexString.append('0');
            hexString.append(hex);
        }
        return hexString.toString();
    }


}
