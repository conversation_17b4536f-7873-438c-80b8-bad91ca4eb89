package top.continew.admin.controller.crm;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.date.DatePattern;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.biz.model.entity.SalesPersonnelMonthlyDataDO;
import top.continew.admin.biz.model.query.CustomerStatQuery;
import top.continew.admin.biz.model.query.PerformanceStatisticsQuery;
import top.continew.admin.biz.model.query.crm.SalesPerformanceStatQuery;
import top.continew.admin.biz.model.req.CustomerPerformanceInfoReq;
import top.continew.admin.biz.model.req.CustomerPerformanceOpportunityReq;
import top.continew.admin.biz.model.req.SalesPerformanceSubmitReq;
import top.continew.admin.biz.model.req.SalesPersonnelConfigReq;
import top.continew.admin.biz.model.resp.*;
import top.continew.admin.biz.model.resp.crm.SalesPerformanceStatResp;
import top.continew.admin.biz.service.crm.SalesPerformanceStatService;
import top.continew.admin.common.enums.DisEnableStatusEnum;
import top.continew.admin.system.enums.JobRankEnum;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.log.annotation.Log;

import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.List;

/**
 * 商务业绩统计 API
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Tag(name = "商务业绩统计 API")
@RestController
@RequestMapping(value = "/biz/salesPerformanceStat")
@RequiredArgsConstructor
public class SalesPerformanceStatController {

    private final SalesPerformanceStatService salesPerformanceStatService;

    @PostMapping("/list")
    @Operation(summary = "查询商务业绩统计", description = "根据条件查询商务业绩统计数据")
    @Log(ignore = true)
    public List<SalesPerformanceStatResp> list(@RequestBody @Valid SalesPerformanceStatQuery query) {
        return salesPerformanceStatService.listSalesPerformanceStat(query);
    }

    @GetMapping("/summary")
    @Operation(summary = "汇总")
    @Log(ignore = true)
    public SalesDataSummaryResp summary(
            @NotNull(message = "开始时间不能为空")
            @RequestParam("startTime")
            @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
            LocalDateTime startTime,
            @NotNull(message = "结束时间不能为空")
            @RequestParam("endTime")
            @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
            LocalDateTime endTime,
            DisEnableStatusEnum userStatus) {
        return salesPerformanceStatService.getSalesDataSummary(startTime, endTime,userStatus);
    }

    @GetMapping("/list")
    @Operation(summary = "查询商务业绩统计", description = "根据条件查询商务业绩统计数据")
    @Log(ignore = true)
    public List<SalesPersonnelMonthlyDataResp> list(@ParameterObject @Validated PerformanceStatisticsQuery query) {

        return salesPerformanceStatService.listBusinessPerformanceStat(query);
    }

    @PostMapping("/submit")
    @Operation(summary = "提交绩效数据")
    @Log(ignore = true)
    public void submit(@RequestBody @Validated SalesPerformanceSubmitReq req) {
        salesPerformanceStatService.submit(req);
    }

    @PostMapping("/config/submit")
    @Operation(summary = "提交配置数据")
    @Log(ignore = true)
    public void submitConfig(@RequestBody @Validated SalesPersonnelConfigReq req) {
        salesPerformanceStatService.submitConfig(req);
    }


    @GetMapping("/{id}")
    @Operation(summary = "商务人员月度数据")
    @Log(ignore = true)
    public List<SalesPersonnelMonthlyDataDO> info(@PathVariable Long id, @DateTimeFormat(pattern = DatePattern.NORM_MONTH_PATTERN) YearMonth yearMonth, JobRankEnum jobRank) {
        CheckUtils.throwIfNull(jobRank, "职级不能为空");
        return salesPerformanceStatService.info(id, yearMonth, jobRank);
    }

    @GetMapping("/customerStat")
    @Operation(summary = "新客户业绩统计")
    @Log(ignore = true)
    public List<CustomerPerformanceStatResp> customerStat(@Validated CustomerStatQuery query) {
        return salesPerformanceStatService.customerStat(query);
    }

    @GetMapping("/customerStat/opportunity")
    @Operation(summary = "新客户业绩统计商机详情")
    @Log(ignore = true)
    public List<CustomerPerformanceOpportunityResp> customerStatOpportunity(@Validated CustomerPerformanceOpportunityReq req) {
        return salesPerformanceStatService.customerStatOpportunity(req);
    }


    @GetMapping("/customerStat/customerInfo")
    @Operation(summary = "新客户业绩统计客户信息")
    @Log(ignore = true)
    public List<CustomerPerformanceInfoResp> customerStatCustomerInfo(@Validated CustomerPerformanceInfoReq req) {
        return salesPerformanceStatService.customerStatCustomerInfo(req);
    }

    @GetMapping("/refundCustomerStat")
    @Operation(summary = "退款客户统计")
    @Log(ignore = true)
    public List<CustomerRefundPerformanceStatResp> refundCustomerStat(@Validated CustomerStatQuery query) {
        return salesPerformanceStatService.refundCustomerStat(query);
    }

    @GetMapping("/refundCustomerStatInfo")
    @Operation(summary = "退款客户统计详情")
    @Log(ignore = true)
    public List<RefundCustomerStatInfoResp> refundCustomerStatInfo(@Validated CustomerPerformanceInfoReq req) {
        return salesPerformanceStatService.refundCustomerStatInfo(req);
    }

}