package top.continew.admin.controller.biz;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.model.entity.WhiteEmailDO;
import top.continew.admin.biz.model.query.BusinessManagerUserQuery;
import top.continew.admin.biz.model.req.BusinessManagerUserReq;
import top.continew.admin.biz.model.req.WhiteEmailReq;
import top.continew.admin.biz.model.resp.BusinessManagerUserDetailResp;
import top.continew.admin.biz.model.resp.BusinessManagerUserResp;
import top.continew.admin.biz.service.BusinessManagerUserService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;

/**
 * bm管理员管理 API
 *
 * <AUTHOR>
 * @since 2025/04/22 16:45
 */
@Tag(name = "bm管理员管理 API")
@RestController
@CrudRequestMapping(value = "/biz/businessManagerUser", api = {Api.PAGE, Api.EXPORT})
public class BusinessManagerUserController extends BaseController<BusinessManagerUserService, BusinessManagerUserResp, BusinessManagerUserDetailResp, BusinessManagerUserQuery, BusinessManagerUserReq> {

    @Operation(summary = "添加邮箱白名单", description = "添加邮箱白名单")
    @PostMapping("addWhiteList")
    public void addWhiteList(@Validated @RequestBody WhiteEmailReq req) {
        this.baseService.addWhiteList(req.getEmail());
    }
}