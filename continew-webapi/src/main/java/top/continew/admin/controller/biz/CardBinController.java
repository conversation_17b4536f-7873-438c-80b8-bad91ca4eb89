package top.continew.admin.controller.biz;

import cn.dev33.satoken.annotation.SaIgnore;
import io.swagger.v3.oas.annotations.Operation;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.CardBinQuery;
import top.continew.admin.biz.model.req.CardBinReq;
import top.continew.admin.biz.model.resp.CardBinDetailResp;
import top.continew.admin.biz.model.resp.CardBinResp;
import top.continew.admin.biz.service.CardBinService;

/**
 * 卡头管理管理 API
 *
 * <AUTHOR>
 * @since 2025/09/02 15:44
 */
@Tag(name = "卡头管理管理 API")
@RestController
@CrudRequestMapping(value = "/biz/cardBin", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class CardBinController extends BaseController<CardBinService, CardBinResp, CardBinDetailResp, CardBinQuery, CardBinReq> {


    @PostMapping("/sync")
    @Operation(summary = "同步卡头数据", description = "同步卡头数据")
    @SaIgnore
    public void sync(CardPlatformEnum cardPlatform) {
        baseService.syncCardBin(cardPlatform);
    }


}