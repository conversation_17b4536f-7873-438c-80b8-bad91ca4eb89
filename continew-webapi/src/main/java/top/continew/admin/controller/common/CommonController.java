/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.controller.common;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.StrUtil;
import com.alicp.jetcache.anno.Cached;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.x.file.storage.core.FileInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.model.query.*;
import top.continew.admin.biz.model.query.crm.SocialAccountQuery;
import top.continew.admin.biz.model.query.crm.SourceQuery;
import top.continew.admin.biz.model.resp.DictLabelValueResp;
import top.continew.admin.biz.model.resp.ProfitTypeResp;
import top.continew.admin.biz.service.*;
import top.continew.admin.biz.service.crm.SocialAccountService;
import top.continew.admin.biz.service.crm.SourceService;
import top.continew.admin.common.constant.CacheConstants;
import top.continew.admin.system.enums.OptionCategoryEnum;
import top.continew.admin.system.model.query.*;
import top.continew.admin.system.model.resp.FileUploadResp;
import top.continew.admin.system.service.*;
import top.continew.starter.core.validation.ValidationUtils;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.LabelValueResp;
import top.continew.starter.log.annotation.Log;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 公共 API
 *
 * <AUTHOR>
 * @since 2023/1/22 21:48
 */
@Tag(name = "公共 API")
@Log(ignore = true)
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/common")
public class CommonController {

    private final FileService fileService;
    private final DeptService deptService;
    private final MenuService menuService;
    private final UserService userService;
    private final RoleService roleService;
    private final DictItemService dictItemService;
    private final OptionService optionService;
    private final CustomerService customerService;
    private final AdAccountService adAccountService;
    private final BusinessManagerService businessManagerService;
    private final BusinessManagerChannelService businessManagerChannelService;
    private final FbChannelService fbChannelService;
    private final CardService cardService;
    private final SourceService sourceService;
    private final SocialAccountService socialAccountService;
    private final TagService tagService;
    private final AgentService agentService;
    private final AdProductService adProductService;
    private final ProfitTypeService profitTypeService;
    private final TransactionUserService transactionUserService;


    @Operation(summary = "上传文件", description = "上传文件")
    @PostMapping("/file")
    public FileUploadResp upload(@NotNull(message = "文件不能为空") MultipartFile file) {
        ValidationUtils.throwIf(file::isEmpty, "文件不能为空");
        FileInfo fileInfo = fileService.upload(file);
        return FileUploadResp.builder().url(fileInfo.getUrl()).build();
    }

    @Operation(summary = "查询部门树", description = "查询树结构的部门列表")
    @GetMapping("/tree/dept")
    public List<Tree<Long>> listDeptTree(DeptQuery query, SortQuery sortQuery) {
        return deptService.tree(query, sortQuery, true);
    }

    @Operation(summary = "查询菜单树", description = "查询树结构的菜单列表")
    @GetMapping("/tree/menu")
    public List<Tree<Long>> listMenuTree(MenuQuery query, SortQuery sortQuery) {
        return menuService.tree(query, sortQuery, true);
    }

    @Operation(summary = "查询用户字典", description = "查询用户字典列表")
    @GetMapping("/dict/user")
    public List<LabelValueResp> listUserDict(UserQuery query, SortQuery sortQuery) {
        return userService.listDict(query, sortQuery);
    }


    @Operation(summary = "查询所有用户字典", description = "查询用户字典列表")
    @GetMapping("/dict/all_user")
    public List<LabelValueResp> listAllUserDict(UserQuery query, SortQuery sortQuery) {
        return userService.listAllDict(query, sortQuery);
    }


    @Operation(summary = "查询来源字典", description = "查询来源字典")
    @GetMapping("/dict/source")
    public List<LabelValueResp> listSourceDict(SourceQuery query, SortQuery sortQuery) {
        return sourceService.listDict(query, sortQuery);
    }

    @Operation(summary = "查询社交账号字典", description = "查询社交账号字典")
    @GetMapping("/dict/socialAccount")
    public List<LabelValueResp> listSocialAccountDict(SocialAccountQuery query, SortQuery sortQuery) {
        return socialAccountService.listDict(query, sortQuery);
    }

    @Operation(summary = "查询角色字典", description = "查询角色字典列表")
    @GetMapping("/dict/role")
    public List<LabelValueResp> listRoleDict(RoleQuery query, SortQuery sortQuery) {
        return roleService.listDict(query, sortQuery);
    }

    @GetMapping("/dict/adAccount")
    public List<LabelValueResp> listAdAccountDict(AdAccountQuery query, SortQuery sortQuery) {
        return adAccountService.listDict(query, sortQuery);
    }

    @GetMapping("/dict/customer")
    public List<LabelValueResp> listCustomerDict(CustomerQuery query, SortQuery sortQuery) {
        return customerService.listDict(query, sortQuery);
    }

    @GetMapping("/dict/agent")
    public List<LabelValueResp> listAgentDict(AgentQuery query, SortQuery sortQuery) {
        return agentService.listDict(query, sortQuery);
    }

    @GetMapping("/dict/businessManager")
    public List<LabelValueResp> listBusinessManagerDict(BusinessManagerQuery query, SortQuery sortQuery) {
        return businessManagerService.listDict(query, sortQuery);
    }

    @GetMapping("/dict/businessManager1")
    public List<LabelValueResp> listBusinessManager1Dict() {
        return businessManagerService.listBM1();
    }

    @GetMapping("/dict/businessManagerChannel")
    public List<LabelValueResp> listBusinessManagerChannelDict(BusinessManagerChannelQuery query, SortQuery sortQuery) {
        return businessManagerChannelService.listDict(query, sortQuery);
    }

    @GetMapping("/dict/fbChannel")
    public List<LabelValueResp> listFbChannelDict(FbChannelQuery query, SortQuery sortQuery) {
        return fbChannelService.listDict(query, sortQuery);
    }

    @GetMapping("/dict/adProduct")
    public List<LabelValueResp> listAdProductDict(AdProductQuery query, SortQuery sortQuery) {
        return adProductService.listDict(query, sortQuery);
    }

    @GetMapping("/dict/profitType")
    public List<LabelValueResp> listProfitTypeDict(ProfitTypeQuery query, SortQuery sortQuery) {
        return profitTypeService.listDict(query, sortQuery);
    }

    @GetMapping("/dict/transactionUser")
    public List<LabelValueResp> listTransactionUserDict(TransactionUserQuery query, SortQuery sortQuery) {
        return transactionUserService.listDict(query, sortQuery);
    }

    @GetMapping("/cardPlatform/balance")
    public BigDecimal getCardPlatformBalance(CardPlatformEnum platform) {
        return cardService.getPlatformBalance(platform);
    }

    @Operation(summary = "查询字典", description = "查询字典列表")
    @Parameter(name = "code", description = "字典编码", example = "notice_type", in = ParameterIn.PATH)
    @GetMapping("/dict/{code}")
    public List<DictLabelValueResp> listDict(@PathVariable String code) {
        if("business_manager_type".equals(code)) {
            ProfitTypeQuery query = new ProfitTypeQuery();
            query.setAdPlatform(1);
            query.setCat("ad_account");
            SortQuery sortQuery = new SortQuery();
            List<LabelValueResp> types = profitTypeService.listDict(query, sortQuery);
            return types.stream().map(type -> new DictLabelValueResp<>(type.getLabel(), type.getValue(), type.getExtra())).collect(Collectors.toList());

        }
        return dictItemService.listByDictCode(code);
    }

    @SaIgnore
    @Operation(summary = "查询系统配置参数", description = "查询系统配置参数")
    @GetMapping("/dict/option/site")
    @Cached(key = "'SITE'", name = CacheConstants.OPTION_KEY_PREFIX)
    public List<LabelValueResp<String>> listSiteOptionDict() {
        OptionQuery optionQuery = new OptionQuery();
        optionQuery.setCategory(OptionCategoryEnum.SITE.name());
        return optionService.list(optionQuery)
                .stream()
                .map(option -> new LabelValueResp<>(option.getCode(), StrUtil.nullToDefault(option.getValue(), option
                        .getDefaultValue())))
                .toList();
    }

    @GetMapping("/dict/tag")
    public List<LabelValueResp> listTagDict(TagQuery query, SortQuery sortQuery) {
        return tagService.listDict(query, sortQuery);
    }
}
