package top.continew.admin.controller.biz;

import jakarta.servlet.http.HttpServletResponse;
import top.continew.admin.biz.model.query.ProfitStatQuery;
import top.continew.admin.biz.model.resp.*;
import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.biz.model.query.ProfitQuery;
import top.continew.admin.biz.model.req.ProfitReq;
import top.continew.admin.biz.service.ProfitService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.file.excel.util.ExcelUtils;
import top.continew.starter.log.annotation.Log;

import java.util.List;

/**
 * 利润管理 API
 *
 * <AUTHOR>
 * @since 2025/08/16 16:14
 */
@Tag(name = "利润管理 API")
@RestController
@CrudRequestMapping(value = "/biz/profit", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
public class ProfitController extends BaseController<ProfitService, ProfitResp, ProfitDetailResp, ProfitQuery, ProfitReq> {
    @GetMapping("/stat/date")
    public PageResp<ProfitStatByDateResp> statByDate(ProfitStatQuery query, PageQuery pageQuery){
        return this.baseService.selectStatByDatePage(query, pageQuery);
    }

    @GetMapping("/stat/type")
    public List<ProfitStatByTypeResp> statByType(ProfitStatQuery query, String[] sort){
        return this.baseService.selectStatByTypeList(query,sort);
    }

    @Log(ignore = true)
    @GetMapping("stat/date/export")
    public void statByDateExport(ProfitStatQuery query, HttpServletResponse response) {
        List<ProfitStatByDateResp> list = this.baseService.selectStatByDateList(query);
        ExcelUtils.export(list, "导出数据", ProfitStatByDateResp.class, response);
    }

    @Log(ignore = true)
    @GetMapping("stat/type/export")
    public void statByTypeExport(ProfitStatQuery query, String[] sort, HttpServletResponse response) {
        /**
         * 查询全部的type类型
         */
        List<ProfitStatByTypeResp> list = this.baseService.selectStatByTypeList(query, sort);
        ExcelUtils.export(list, "导出数据", ProfitStatByTypeResp.class, response);
    }
}