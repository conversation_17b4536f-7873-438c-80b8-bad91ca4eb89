package top.continew.admin.job;

import cn.hutool.extra.spring.SpringUtil;
import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import top.continew.admin.biz.event.BusinessTelegramMessageEvent;
import top.continew.admin.biz.model.resp.SalesDailyStatsResp;
import top.continew.admin.biz.service.crm.SalesDailyDataService;

import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class SalesJob {

    private final SalesDailyDataService salesDailyDataService;

    @Value("${businessTelegram.businessInnerChatId}")
    private Long chatId;


    @JobExecutor(name = "sendPreviousDaySalesStatistics")
    public void sendPreviousDaySalesStatistics() {
        List<SalesDailyStatsResp> resp = salesDailyDataService.getPreviousDaySalesStatistics();


        List<SalesDailyStatsResp> sortedResp = resp.stream()
                .sorted((a, b) -> Integer.compare(b.getTotalCount(), a.getTotalCount()))
                .toList();

        String msg = formatAsTable(sortedResp);
        SpringUtil.publishEvent(new BusinessTelegramMessageEvent(SendMessage.builder()
                .chatId(chatId)
                .text(msg)
                .build()));
    }


    public static String formatAsTable(List<SalesDailyStatsResp> statsList) {
        String yesterdayDate = java.time.LocalDate.now().minusDays(1).toString();
        if (statsList == null || statsList.isEmpty()) {
            return String.format("""
                    📊 %s 客咨统计
                    ━━━━━━━━━━━━━━━━━━━━
                    暂无数据""", yesterdayDate);
        }

        StringBuilder sb = new StringBuilder();

        // 美化的表头
        sb.append(String.format("📊 %s 客咨统计\n", yesterdayDate));
        sb.append("━━━━━━━━━━━━━━━━━━━━\n");
        sb.append("👤 商务人员    📈 客咨数据\n");
        sb.append("────────────────────\n");

        for (SalesDailyStatsResp stat : statsList) {
            String name = stat.getName();

            // 处理名字长度，中文字符按2个字符计算
            String displayName = formatChineseName(name);

            // 格式化数据，使用更直观的显示方式
            String dataString = String.format("总:%d 微:%d TG:%d 拉群:%d 成交:%d",
                    stat.getTotalCount(),
                    stat.getWechatCount(),
                    stat.getTelegramCount(),
                    stat.getGroupCount(),
                    stat.getDealCount());

            // 针对 Telegram 优化的两端对齐
            sb.append(displayName);

            // 根据名字长度动态调整空格数，确保在 Telegram 中正确对齐
            int spaces;
            if (displayName.length() <= 2) {
                spaces = 15; // 短名字（如"张三"）
            } else if (displayName.length() <= 4) {
                spaces = 13; // 中等名字（如"李四"）
            } else {
                spaces = 11; // 长名字
            }

            sb.append(" ".repeat(spaces));
            sb.append(dataString);
            sb.append("\n");
        }

        sb.append("────────────────────");
        return sb.toString();
    }

    /**
     * 计算字符串的显示宽度（中文字符算2个宽度，英文字符算1个宽度，emoji算2个宽度）
     */
    private static int getDisplayWidth(String str) {
        if (str == null) return 0;
        int width = 0;
        for (int i = 0; i < str.length(); ) {
            int codePoint = str.codePointAt(i);
            if (codePoint >= 0x4e00 && codePoint <= 0x9fff) {
                width += 2; // 中文字符
            } else if (codePoint >= 0x1F000 && codePoint <= 0x1F9FF) {
                width += 2; // emoji 字符
            } else {
                width += 1; // 英文字符和其他字符
            }
            i += Character.charCount(codePoint);
        }
        return width;
    }

    /**
     * 格式化中文名字，考虑中文字符宽度
     */
    private static String formatChineseName(String name) {
        if (name == null) return "";

        if (name.length() <= 8) {
            return name;
        }
        return name.substring(0, 8) + "..";
    }

}